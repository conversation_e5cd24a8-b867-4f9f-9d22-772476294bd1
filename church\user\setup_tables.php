<?php
/**
 * Quick Database Setup for New Features
 * Creates missing tables and columns for the advanced features
 */

require_once '../config.php';

echo "<h1>🔧 Database Setup for Advanced Features</h1>";

try {
    // 1. Create prayer_requests table
    echo "<h3>Setting up Prayer Requests...</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'prayer_requests'");
    if (!$stmt->fetch()) {
        $sql = "CREATE TABLE prayer_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(50) DEFAULT 'personal',
            privacy_level VARCHAR(20) DEFAULT 'private',
            status VARCHAR(20) DEFAULT 'active',
            is_urgent BOOLEAN DEFAULT FALSE,
            is_anonymous BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_status (status),
            INDEX idx_privacy_level (privacy_level),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "✅ prayer_requests table created<br>";
    } else {
        echo "✅ prayer_requests table already exists<br>";
    }

    // 2. Create prayer_responses table
    $stmt = $pdo->query("SHOW TABLES LIKE 'prayer_responses'");
    if (!$stmt->fetch()) {
        $sql = "CREATE TABLE prayer_responses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prayer_request_id INT NOT NULL,
            member_id INT NOT NULL,
            response_type VARCHAR(50) DEFAULT 'prayed',
            comment TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_prayer_request_id (prayer_request_id),
            INDEX idx_member_id (member_id),
            FOREIGN KEY (prayer_request_id) REFERENCES prayer_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "✅ prayer_responses table created<br>";
    } else {
        echo "✅ prayer_responses table already exists<br>";
    }

    // 3. Create family_relationships table
    echo "<h3>Setting up Family Management...</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'family_relationships'");
    if (!$stmt->fetch()) {
        $sql = "CREATE TABLE family_relationships (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            related_member_id INT NOT NULL,
            relationship_type VARCHAR(50) NOT NULL,
            can_manage_profile BOOLEAN DEFAULT FALSE,
            can_rsvp_for_member BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_related_member_id (related_member_id),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (related_member_id) REFERENCES members(id) ON DELETE CASCADE,
            UNIQUE KEY unique_relationship (member_id, related_member_id)
        )";
        $pdo->exec($sql);
        echo "✅ family_relationships table created<br>";
    } else {
        echo "✅ family_relationships table already exists<br>";
    }

    // 4. Add missing columns to members table
    echo "<h3>Enhancing Members Table...</h3>";
    $new_columns = [
        'bio' => "ALTER TABLE members ADD COLUMN bio TEXT",
        'interests' => "ALTER TABLE members ADD COLUMN interests TEXT",
        'emergency_contact_name' => "ALTER TABLE members ADD COLUMN emergency_contact_name VARCHAR(255)",
        'emergency_contact_phone' => "ALTER TABLE members ADD COLUMN emergency_contact_phone VARCHAR(20)",
        'emergency_contact_relationship' => "ALTER TABLE members ADD COLUMN emergency_contact_relationship VARCHAR(100)",
        'medical_conditions' => "ALTER TABLE members ADD COLUMN medical_conditions TEXT",
        'allergies' => "ALTER TABLE members ADD COLUMN allergies TEXT",
        'preferred_communication' => "ALTER TABLE members ADD COLUMN preferred_communication VARCHAR(20) DEFAULT 'email'",
        'anniversary_date' => "ALTER TABLE members ADD COLUMN anniversary_date DATE",
        'baptism_date' => "ALTER TABLE members ADD COLUMN baptism_date DATE",
        'volunteer_interests' => "ALTER TABLE members ADD COLUMN volunteer_interests TEXT",
        'availability_schedule' => "ALTER TABLE members ADD COLUMN availability_schedule TEXT",
        'social_media_links' => "ALTER TABLE members ADD COLUMN social_media_links JSON",
        'privacy_settings' => "ALTER TABLE members ADD COLUMN privacy_settings JSON",
        'notification_settings' => "ALTER TABLE members ADD COLUMN notification_settings JSON",
        'profile_photo_path' => "ALTER TABLE members ADD COLUMN profile_photo_path VARCHAR(255)",
        'cover_photo_path' => "ALTER TABLE members ADD COLUMN cover_photo_path VARCHAR(255)"
    ];

    // Check existing columns
    $stmt = $pdo->query("DESCRIBE members");
    $existing_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

    foreach ($new_columns as $column_name => $sql) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $pdo->exec($sql);
                echo "✅ Added $column_name to members table<br>";
            } catch (PDOException $e) {
                echo "⚠️ Could not add $column_name: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✅ $column_name already exists in members table<br>";
        }
    }

    echo "<h2>🎉 Database setup completed successfully!</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ Ready to test features:</h4>";
    echo "<ul>";
    echo "<li><a href='prayer_requests.php' style='color: #155724; font-weight: bold;'>Prayer Requests System</a></li>";
    echo "<li><a href='family_management.php' style='color: #155724; font-weight: bold;'>Family Management System</a></li>";
    echo "<li><a href='profile.php' style='color: #155724; font-weight: bold;'>Enhanced Profile Management</a></li>";
    echo "<li><a href='dashboard.php' style='color: #155724; font-weight: bold;'>Updated Dashboard</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<h2>❌ Error during setup:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}
h1, h2, h3 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
