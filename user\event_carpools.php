<?php
/**
 * Event Carpooling Coordination
 * Allows users to offer rides or find rides to events
 */

session_start();
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Check if user has RSVP'd to event
try {
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ? AND status = 'attending'");
    $stmt->execute([$event_id, $userId]);
    $user_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $user_rsvp = null;
}

// Handle carpool creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_carpool'])) {
    if (!$user_rsvp) {
        $error = "You must RSVP to the event before offering a carpool.";
    } else {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO event_carpools 
                (event_id, driver_member_id, departure_location, departure_time, 
                 available_seats, return_time, contact_info, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $event_id,
                $userId,
                $_POST['departure_location'],
                $_POST['departure_time'],
                $_POST['available_seats'],
                $_POST['return_time'] ?: null,
                $_POST['contact_info'],
                $_POST['notes']
            ]);
            
            $message = "Your carpool offer has been created successfully!";
        } catch (PDOException $e) {
            $error = "Error creating carpool: " . $e->getMessage();
        }
    }
}

// Handle carpool request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_ride'])) {
    if (!$user_rsvp) {
        $error = "You must RSVP to the event before requesting a ride.";
    } else {
        try {
            $carpool_id = $_POST['carpool_id'];
            
            // Check if user already requested this carpool
            $stmt = $pdo->prepare("SELECT id FROM carpool_passengers WHERE carpool_id = ? AND passenger_member_id = ?");
            $stmt->execute([$carpool_id, $userId]);
            $existing_request = $stmt->fetch();
            
            if ($existing_request) {
                $error = "You have already requested this ride.";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO carpool_passengers 
                    (carpool_id, passenger_member_id, pickup_location, special_instructions)
                    VALUES (?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $carpool_id,
                    $userId,
                    $_POST['pickup_location'],
                    $_POST['special_instructions']
                ]);
                
                $message = "Your ride request has been sent to the driver!";
            }
        } catch (PDOException $e) {
            $error = "Error requesting ride: " . $e->getMessage();
        }
    }
}

// Get available carpools
try {
    $stmt = $pdo->prepare("
        SELECT ec.*, m.full_name as driver_name, m.phone_number as driver_phone,
               COUNT(cp.id) as passenger_count,
               (ec.available_seats - COUNT(cp.id)) as remaining_seats,
               user_request.id as user_request_id,
               user_request.status as user_request_status
        FROM event_carpools ec
        JOIN members m ON ec.driver_member_id = m.id
        LEFT JOIN carpool_passengers cp ON ec.id = cp.carpool_id AND cp.status != 'cancelled'
        LEFT JOIN carpool_passengers user_request ON ec.id = user_request.carpool_id AND user_request.passenger_member_id = ?
        WHERE ec.event_id = ? AND ec.is_active = 1
        GROUP BY ec.id
        ORDER BY ec.departure_time
    ");
    $stmt->execute([$userId, $event_id]);
    $carpools = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error loading carpools: " . $e->getMessage();
    $carpools = [];
}

// Get user's own carpool if any
try {
    $stmt = $pdo->prepare("
        SELECT ec.*, COUNT(cp.id) as passenger_count
        FROM event_carpools ec
        LEFT JOIN carpool_passengers cp ON ec.id = cp.carpool_id AND cp.status != 'cancelled'
        WHERE ec.event_id = ? AND ec.driver_member_id = ? AND ec.is_active = 1
        GROUP BY ec.id
    ");
    $stmt->execute([$event_id, $userId]);
    $user_carpool = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $user_carpool = null;
}

// Get passengers for user's carpool
$carpool_passengers = [];
if ($user_carpool) {
    try {
        $stmt = $pdo->prepare("
            SELECT cp.*, m.full_name, m.phone_number, m.email
            FROM carpool_passengers cp
            JOIN members m ON cp.passenger_member_id = m.id
            WHERE cp.carpool_id = ?
            ORDER BY cp.created_at
        ");
        $stmt->execute([$user_carpool['id']]);
        $carpool_passengers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $carpool_passengers = [];
    }
}

// Get user data
$userData = $userAuth->getUserById($userId);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carpooling - <?php echo htmlspecialchars($event['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/user-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                <li class="breadcrumb-item"><a href="event_detail.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($event['title']); ?></a></li>
                <li class="breadcrumb-item active">Carpooling</li>
            </ol>
        </nav>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Event Info -->
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="mb-2"><i class="bi bi-car-front"></i> Carpooling for <?php echo htmlspecialchars($event['title']); ?></h2>
                <p class="text-muted mb-0">
                    <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?>
                    <?php if ($event['location']): ?>
                        | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- RSVP Check -->
        <?php if (!$user_rsvp): ?>
            <div class="alert alert-warning">
                <h5><i class="bi bi-exclamation-triangle"></i> RSVP Required</h5>
                <p class="mb-0">You need to RSVP to the event before you can participate in carpooling. 
                <a href="event_detail.php?id=<?php echo $event_id; ?>" class="alert-link">Click here to RSVP</a>.</p>
            </div>
        <?php else: ?>
            
            <!-- User's Carpool (if offering) -->
            <?php if ($user_carpool): ?>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-car-front-fill"></i> Your Carpool Offer</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <p><strong>Departure:</strong> <?php echo htmlspecialchars($user_carpool['departure_location']); ?></p>
                                <p><strong>Time:</strong> <?php echo date('M j, Y g:i A', strtotime($user_carpool['departure_time'])); ?></p>
                                <?php if ($user_carpool['return_time']): ?>
                                    <p><strong>Return:</strong> <?php echo date('g:i A', strtotime($user_carpool['return_time'])); ?></p>
                                <?php endif; ?>
                                <p><strong>Available Seats:</strong> <?php echo $user_carpool['available_seats']; ?></p>
                                <p><strong>Passengers:</strong> <?php echo $user_carpool['passenger_count']; ?></p>
                            </div>
                            <div class="col-md-4 text-end">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#passengersModal">
                                    <i class="bi bi-people"></i> View Passengers
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Offer Carpool -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-plus-circle"></i> Offer a Ride</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Have extra space in your car? Offer a ride to fellow attendees!</p>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createCarpoolModal">
                            <i class="bi bi-car-front"></i> Offer a Ride
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Available Carpools -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-search"></i> Available Rides</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($carpools)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-car-front text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">No Rides Available</h5>
                            <p class="text-muted">No one has offered a ride yet. Be the first to offer!</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($carpools as $carpool): ?>
                                <?php if ($carpool['driver_member_id'] != $userId): // Don't show user's own carpool ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0"><?php echo htmlspecialchars($carpool['driver_name']); ?></h6>
                                                    <span class="badge bg-primary"><?php echo $carpool['remaining_seats']; ?> seats left</span>
                                                </div>
                                                
                                                <p class="card-text small mb-2">
                                                    <i class="bi bi-geo-alt"></i> <strong>From:</strong> <?php echo htmlspecialchars($carpool['departure_location']); ?><br>
                                                    <i class="bi bi-clock"></i> <strong>Departure:</strong> <?php echo date('M j, g:i A', strtotime($carpool['departure_time'])); ?>
                                                    <?php if ($carpool['return_time']): ?>
                                                        <br><i class="bi bi-arrow-return-left"></i> <strong>Return:</strong> <?php echo date('g:i A', strtotime($carpool['return_time'])); ?>
                                                    <?php endif; ?>
                                                </p>

                                                <?php if ($carpool['notes']): ?>
                                                    <p class="card-text small text-muted">
                                                        <i class="bi bi-chat-text"></i> <?php echo htmlspecialchars($carpool['notes']); ?>
                                                    </p>
                                                <?php endif; ?>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <?php echo $carpool['passenger_count']; ?>/<?php echo $carpool['available_seats']; ?> passengers
                                                    </small>
                                                    
                                                    <?php if ($carpool['user_request_id']): ?>
                                                        <span class="badge bg-<?php echo $carpool['user_request_status'] === 'confirmed' ? 'success' : 'warning'; ?>">
                                                            <?php echo ucfirst($carpool['user_request_status']); ?>
                                                        </span>
                                                    <?php elseif ($carpool['remaining_seats'] > 0): ?>
                                                        <button type="button" class="btn btn-primary btn-sm" 
                                                                onclick="requestRide(<?php echo $carpool['id']; ?>, '<?php echo htmlspecialchars($carpool['driver_name']); ?>')">
                                                            <i class="bi bi-hand-thumbs-up"></i> Request Ride
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Full</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Create Carpool Modal -->
    <div class="modal fade" id="createCarpoolModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Offer a Ride</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Departure Location *</label>
                            <input type="text" class="form-control" name="departure_location" required
                                   placeholder="Where will you be leaving from?">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Departure Time *</label>
                            <input type="datetime-local" class="form-control" name="departure_time" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Return Time (Optional)</label>
                            <input type="datetime-local" class="form-control" name="return_time">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Available Seats *</label>
                            <input type="number" class="form-control" name="available_seats" min="1" max="8" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Contact Information</label>
                            <input type="text" class="form-control" name="contact_info" 
                                   value="<?php echo htmlspecialchars($userData['phone_number'] ?? $userData['email']); ?>"
                                   placeholder="Phone number or preferred contact method">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="Any additional information for passengers..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="create_carpool" class="btn btn-success">Offer Ride</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Request Ride Modal -->
    <div class="modal fade" id="requestRideModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Request a Ride</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="carpool_id" id="request_carpool_id">
                        
                        <p>You're requesting a ride from <strong id="request_driver_name"></strong>.</p>

                        <div class="mb-3">
                            <label class="form-label">Pickup Location</label>
                            <input type="text" class="form-control" name="pickup_location" 
                                   placeholder="Where would you like to be picked up?">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Special Instructions</label>
                            <textarea class="form-control" name="special_instructions" rows="3" 
                                      placeholder="Any special instructions or information for the driver..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="request_ride" class="btn btn-primary">Send Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Passengers Modal -->
    <?php if ($user_carpool): ?>
        <div class="modal fade" id="passengersModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Your Passengers</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php if (empty($carpool_passengers)): ?>
                            <p class="text-muted">No passengers have requested a ride yet.</p>
                        <?php else: ?>
                            <?php foreach ($carpool_passengers as $passenger): ?>
                                <div class="card mb-2">
                                    <div class="card-body py-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?php echo htmlspecialchars($passenger['full_name']); ?></strong>
                                                <span class="badge bg-<?php echo $passenger['status'] === 'confirmed' ? 'success' : 'warning'; ?> ms-2">
                                                    <?php echo ucfirst($passenger['status']); ?>
                                                </span>
                                                <?php if ($passenger['pickup_location']): ?>
                                                    <br><small class="text-muted">
                                                        <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($passenger['pickup_location']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <?php if ($passenger['phone_number']): ?>
                                                    <a href="tel:<?php echo $passenger['phone_number']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-phone"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function requestRide(carpoolId, driverName) {
            document.getElementById('request_carpool_id').value = carpoolId;
            document.getElementById('request_driver_name').textContent = driverName;
            new bootstrap.Modal(document.getElementById('requestRideModal')).show();
        }

        // Set default departure time to 30 minutes before event
        document.addEventListener('DOMContentLoaded', function() {
            const eventTime = new Date('<?php echo $event['event_date']; ?>');
            const departureTime = new Date(eventTime.getTime() - 30 * 60000); // 30 minutes before
            
            const departureInput = document.querySelector('input[name="departure_time"]');
            if (departureInput) {
                departureInput.value = departureTime.toISOString().slice(0, 16);
            }
        });
    </script>
</body>
</html>
