<?php
/**
 * Profile Management System
 * Comprehensive user profile management with photo uploads and personal information
 */

session_start();
require_once '../church/config.php';
require_once '../church/classes/SecurityManager.php';
require_once '../church/classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['update_profile'])) {
            // Update basic profile information
            $stmt = $pdo->prepare("
                UPDATE members SET 
                full_name = ?, bio = ?, interests = ?, phone_number = ?, 
                emergency_contact_name = ?, emergency_contact_phone = ?, 
                emergency_contact_relationship = ?, medical_conditions = ?, 
                allergies = ?, preferred_communication = ?, anniversary_date = ?,
                baptism_date = ?, volunteer_interests = ?, availability_schedule = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['full_name'],
                $_POST['bio'],
                $_POST['interests'],
                $_POST['phone_number'],
                $_POST['emergency_contact_name'],
                $_POST['emergency_contact_phone'],
                $_POST['emergency_contact_relationship'],
                $_POST['medical_conditions'],
                $_POST['allergies'],
                $_POST['preferred_communication'],
                $_POST['anniversary_date'] ?: null,
                $_POST['baptism_date'] ?: null,
                $_POST['volunteer_interests'],
                $_POST['availability_schedule'],
                $userId
            ]);
            
            $message = "Profile updated successfully!";
        }
        
        if (isset($_POST['update_social_media'])) {
            // Update social media links
            $social_links = json_encode([
                'facebook' => $_POST['facebook'] ?? '',
                'twitter' => $_POST['twitter'] ?? '',
                'instagram' => $_POST['instagram'] ?? '',
                'linkedin' => $_POST['linkedin'] ?? '',
                'website' => $_POST['website'] ?? ''
            ]);
            
            $stmt = $pdo->prepare("UPDATE members SET social_media_links = ? WHERE id = ?");
            $stmt->execute([$social_links, $userId]);
            
            $message = "Social media links updated successfully!";
        }
        
        if (isset($_POST['update_privacy'])) {
            // Update privacy settings
            $privacy_settings = json_encode([
                'profile_visibility' => $_POST['profile_visibility'] ?? 'members',
                'contact_visibility' => $_POST['contact_visibility'] ?? 'family',
                'birthday_visibility' => $_POST['birthday_visibility'] ?? 'members',
                'family_visibility' => $_POST['family_visibility'] ?? 'family',
                'activity_visibility' => $_POST['activity_visibility'] ?? 'members'
            ]);
            
            $stmt = $pdo->prepare("UPDATE members SET privacy_settings = ? WHERE id = ?");
            $stmt->execute([$privacy_settings, $userId]);
            
            $message = "Privacy settings updated successfully!";
        }
        
        if (isset($_POST['update_notifications'])) {
            // Update notification preferences
            $notification_settings = json_encode([
                'email_events' => isset($_POST['email_events']),
                'email_reminders' => isset($_POST['email_reminders']),
                'email_birthdays' => isset($_POST['email_birthdays']),
                'email_prayers' => isset($_POST['email_prayers']),
                'sms_events' => isset($_POST['sms_events']),
                'sms_reminders' => isset($_POST['sms_reminders']),
                'sms_urgent' => isset($_POST['sms_urgent'])
            ]);
            
            $stmt = $pdo->prepare("UPDATE members SET notification_settings = ? WHERE id = ?");
            $stmt->execute([$notification_settings, $userId]);
            
            $message = "Notification preferences updated successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error updating profile: " . $e->getMessage();
    }
}

// Handle photo uploads
if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
    $upload_dir = '../church/uploads/profiles/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $file_extension = strtolower(pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
    
    if (in_array($file_extension, $allowed_extensions)) {
        $filename = 'profile_' . $userId . '_' . time() . '.' . $file_extension;
        $filepath = $upload_dir . $filename;
        
        if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $filepath)) {
            $stmt = $pdo->prepare("UPDATE members SET profile_photo_path = ? WHERE id = ?");
            $stmt->execute(['uploads/profiles/' . $filename, $userId]);
            $message = "Profile photo updated successfully!";
        } else {
            $error = "Failed to upload profile photo.";
        }
    } else {
        $error = "Invalid file type. Please upload JPG, PNG, or GIF files only.";
    }
}

if (isset($_FILES['cover_photo']) && $_FILES['cover_photo']['error'] === UPLOAD_ERR_OK) {
    $upload_dir = '../church/uploads/covers/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $file_extension = strtolower(pathinfo($_FILES['cover_photo']['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
    
    if (in_array($file_extension, $allowed_extensions)) {
        $filename = 'cover_' . $userId . '_' . time() . '.' . $file_extension;
        $filepath = $upload_dir . $filename;
        
        if (move_uploaded_file($_FILES['cover_photo']['tmp_name'], $filepath)) {
            $stmt = $pdo->prepare("UPDATE members SET cover_photo_path = ? WHERE id = ?");
            $stmt->execute(['uploads/covers/' . $filename, $userId]);
            $message = "Cover photo updated successfully!";
        } else {
            $error = "Failed to upload cover photo.";
        }
    } else {
        $error = "Invalid file type. Please upload JPG, PNG, or GIF files only.";
    }
}

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        header("Location: login.php");
        exit();
    }
    
    // Parse JSON fields
    $social_links = json_decode($user['social_media_links'] ?? '{}', true) ?: [];
    $privacy_settings = json_decode($user['privacy_settings'] ?? '{}', true) ?: [];
    $notification_settings = json_decode($user['notification_settings'] ?? '{}', true) ?: [];
    
} catch (PDOException $e) {
    $error = "Error loading profile: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/user-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                <li class="breadcrumb-item active">Profile Management</li>
            </ol>
        </nav>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Profile Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="position-relative">
                            <?php if ($user['profile_photo_path']): ?>
                                <img src="../church/<?php echo htmlspecialchars($user['profile_photo_path']); ?>" 
                                     class="rounded-circle img-fluid" style="width: 150px; height: 150px; object-fit: cover;" 
                                     alt="Profile Photo">
                            <?php else: ?>
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 150px; height: 150px; margin: 0 auto;">
                                    <i class="bi bi-person-fill text-white" style="font-size: 4rem;"></i>
                                </div>
                            <?php endif; ?>
                            <button type="button" class="btn btn-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#photoModal">
                                <i class="bi bi-camera"></i> Change Photo
                            </button>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <h2><?php echo htmlspecialchars($user['full_name']); ?></h2>
                        <p class="text-muted"><?php echo htmlspecialchars($user['email']); ?></p>
                        <?php if ($user['bio']): ?>
                            <p><?php echo nl2br(htmlspecialchars($user['bio'])); ?></p>
                        <?php endif; ?>
                        <div class="d-flex gap-2">
                            <?php if ($user['phone_number']): ?>
                                <span class="badge bg-secondary"><i class="bi bi-phone"></i> <?php echo htmlspecialchars($user['phone_number']); ?></span>
                            <?php endif; ?>
                            <?php if ($user['birth_date']): ?>
                                <span class="badge bg-info"><i class="bi bi-calendar"></i> Born <?php echo date('M j, Y', strtotime($user['birth_date'])); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management Tabs -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                            <i class="bi bi-person"></i> Basic Info
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                            <i class="bi bi-telephone"></i> Contact & Emergency
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                            <i class="bi bi-share"></i> Social Media
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="privacy-tab" data-bs-toggle="tab" data-bs-target="#privacy" type="button" role="tab">
                            <i class="bi bi-shield-lock"></i> Privacy
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                            <i class="bi bi-bell"></i> Notifications
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="profileTabsContent">
                    <!-- Basic Information Tab -->
                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" name="full_name" 
                                               value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" name="phone_number" 
                                               value="<?php echo htmlspecialchars($user['phone_number']); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Bio</label>
                                <textarea class="form-control" name="bio" rows="4" 
                                          placeholder="Tell us about yourself..."><?php echo htmlspecialchars($user['bio']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Interests & Hobbies</label>
                                <textarea class="form-control" name="interests" rows="3" 
                                          placeholder="What are your interests and hobbies?"><?php echo htmlspecialchars($user['interests']); ?></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Anniversary Date</label>
                                        <input type="date" class="form-control" name="anniversary_date" 
                                               value="<?php echo $user['anniversary_date']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Baptism Date</label>
                                        <input type="date" class="form-control" name="baptism_date" 
                                               value="<?php echo $user['baptism_date']; ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Volunteer Interests</label>
                                <textarea class="form-control" name="volunteer_interests" rows="3" 
                                          placeholder="What volunteer activities interest you?"><?php echo htmlspecialchars($user['volunteer_interests']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Availability Schedule</label>
                                <textarea class="form-control" name="availability_schedule" rows="3" 
                                          placeholder="When are you typically available for activities?"><?php echo htmlspecialchars($user['availability_schedule']); ?></textarea>
                            </div>
                            
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Basic Information
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact & Emergency Tab -->
                    <div class="tab-pane fade" id="contact" role="tabpanel">
                        <form method="POST">
                            <h5 class="mb-3">Emergency Contact Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Emergency Contact Name</label>
                                        <input type="text" class="form-control" name="emergency_contact_name" 
                                               value="<?php echo htmlspecialchars($user['emergency_contact_name']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Emergency Contact Phone</label>
                                        <input type="tel" class="form-control" name="emergency_contact_phone" 
                                               value="<?php echo htmlspecialchars($user['emergency_contact_phone']); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Relationship to Emergency Contact</label>
                                <input type="text" class="form-control" name="emergency_contact_relationship" 
                                       value="<?php echo htmlspecialchars($user['emergency_contact_relationship']); ?>"
                                       placeholder="e.g., spouse, parent, sibling, friend">
                            </div>
                            
                            <h5 class="mb-3 mt-4">Medical Information</h5>
                            <div class="mb-3">
                                <label class="form-label">Medical Conditions</label>
                                <textarea class="form-control" name="medical_conditions" rows="3" 
                                          placeholder="Any medical conditions we should be aware of..."><?php echo htmlspecialchars($user['medical_conditions']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Allergies</label>
                                <textarea class="form-control" name="allergies" rows="3" 
                                          placeholder="Any allergies or dietary restrictions..."><?php echo htmlspecialchars($user['allergies']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Preferred Communication Method</label>
                                <select class="form-select" name="preferred_communication">
                                    <option value="email" <?php echo ($user['preferred_communication'] === 'email') ? 'selected' : ''; ?>>Email</option>
                                    <option value="phone" <?php echo ($user['preferred_communication'] === 'phone') ? 'selected' : ''; ?>>Phone</option>
                                    <option value="sms" <?php echo ($user['preferred_communication'] === 'sms') ? 'selected' : ''; ?>>SMS/Text</option>
                                    <option value="whatsapp" <?php echo ($user['preferred_communication'] === 'whatsapp') ? 'selected' : ''; ?>>WhatsApp</option>
                                </select>
                            </div>
                            
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Contact Information
                            </button>
                        </form>
                    </div>

                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="bi bi-facebook text-primary"></i> Facebook</label>
                                        <input type="url" class="form-control" name="facebook"
                                               value="<?php echo htmlspecialchars($social_links['facebook'] ?? ''); ?>"
                                               placeholder="https://facebook.com/yourprofile">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="bi bi-twitter text-info"></i> Twitter</label>
                                        <input type="url" class="form-control" name="twitter"
                                               value="<?php echo htmlspecialchars($social_links['twitter'] ?? ''); ?>"
                                               placeholder="https://twitter.com/yourusername">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="bi bi-instagram text-danger"></i> Instagram</label>
                                        <input type="url" class="form-control" name="instagram"
                                               value="<?php echo htmlspecialchars($social_links['instagram'] ?? ''); ?>"
                                               placeholder="https://instagram.com/yourusername">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="bi bi-linkedin text-primary"></i> LinkedIn</label>
                                        <input type="url" class="form-control" name="linkedin"
                                               value="<?php echo htmlspecialchars($social_links['linkedin'] ?? ''); ?>"
                                               placeholder="https://linkedin.com/in/yourprofile">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-globe"></i> Personal Website</label>
                                <input type="url" class="form-control" name="website"
                                       value="<?php echo htmlspecialchars($social_links['website'] ?? ''); ?>"
                                       placeholder="https://yourwebsite.com">
                            </div>

                            <button type="submit" name="update_social_media" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Social Media Links
                            </button>
                        </form>
                    </div>

                    <!-- Privacy Tab -->
                    <div class="tab-pane fade" id="privacy" role="tabpanel">
                        <form method="POST">
                            <h5 class="mb-3">Privacy Settings</h5>
                            <p class="text-muted">Control who can see your information and activities.</p>

                            <div class="mb-3">
                                <label class="form-label">Profile Visibility</label>
                                <select class="form-select" name="profile_visibility">
                                    <option value="public" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                    <option value="members" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                    <option value="family" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                    <option value="private" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'private' ? 'selected' : ''; ?>>Private</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Contact Information Visibility</label>
                                <select class="form-select" name="contact_visibility">
                                    <option value="public" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                    <option value="members" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                    <option value="family" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                    <option value="private" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'private' ? 'selected' : ''; ?>>Private</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Birthday Visibility</label>
                                <select class="form-select" name="birthday_visibility">
                                    <option value="public" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                    <option value="members" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                    <option value="family" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                    <option value="private" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'private' ? 'selected' : ''; ?>>Private</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Family Information Visibility</label>
                                <select class="form-select" name="family_visibility">
                                    <option value="public" <?php echo ($privacy_settings['family_visibility'] ?? 'family') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                    <option value="members" <?php echo ($privacy_settings['family_visibility'] ?? 'family') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                    <option value="family" <?php echo ($privacy_settings['family_visibility'] ?? 'family') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                    <option value="private" <?php echo ($privacy_settings['family_visibility'] ?? 'family') === 'private' ? 'selected' : ''; ?>>Private</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Activity Feed Visibility</label>
                                <select class="form-select" name="activity_visibility">
                                    <option value="public" <?php echo ($privacy_settings['activity_visibility'] ?? 'members') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                    <option value="members" <?php echo ($privacy_settings['activity_visibility'] ?? 'members') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                    <option value="family" <?php echo ($privacy_settings['activity_visibility'] ?? 'members') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                    <option value="private" <?php echo ($privacy_settings['activity_visibility'] ?? 'members') === 'private' ? 'selected' : ''; ?>>Private</option>
                                </select>
                            </div>

                            <button type="submit" name="update_privacy" class="btn btn-primary">
                                <i class="bi bi-shield-check"></i> Update Privacy Settings
                            </button>
                        </form>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <form method="POST">
                            <h5 class="mb-3">Email Notifications</h5>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="email_events" id="email_events"
                                       <?php echo ($notification_settings['email_events'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_events">
                                    Event announcements and updates
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="email_reminders" id="email_reminders"
                                       <?php echo ($notification_settings['email_reminders'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_reminders">
                                    Event reminders
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="email_birthdays" id="email_birthdays"
                                       <?php echo ($notification_settings['email_birthdays'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_birthdays">
                                    Birthday and anniversary notifications
                                </label>
                            </div>
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" name="email_prayers" id="email_prayers"
                                       <?php echo ($notification_settings['email_prayers'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_prayers">
                                    Prayer request updates
                                </label>
                            </div>

                            <h5 class="mb-3">SMS Notifications</h5>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="sms_events" id="sms_events"
                                       <?php echo ($notification_settings['sms_events'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_events">
                                    Important event notifications
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="sms_reminders" id="sms_reminders"
                                       <?php echo ($notification_settings['sms_reminders'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_reminders">
                                    Event reminders (24 hours before)
                                </label>
                            </div>
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" name="sms_urgent" id="sms_urgent"
                                       <?php echo ($notification_settings['sms_urgent'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_urgent">
                                    Urgent notifications only
                                </label>
                            </div>

                            <button type="submit" name="update_notifications" class="btn btn-primary">
                                <i class="bi bi-bell-fill"></i> Update Notification Preferences
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Photo Upload Modal -->
    <div class="modal fade" id="photoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Photos</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Profile Photo</label>
                            <input type="file" class="form-control" name="profile_photo" accept="image/*">
                            <div class="form-text">Upload a square image for best results. Max size: 5MB</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Cover Photo</label>
                            <input type="file" class="form-control" name="cover_photo" accept="image/*">
                            <div class="form-text">Upload a wide image (16:9 ratio recommended). Max size: 5MB</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload"></i> Upload Photos
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
