<?php
/**
 * Update Admin Name to be Organization-Agnostic
 * This script updates the admin full_name from "Church Administrator" to "Administrator"
 */

require_once __DIR__ . '/../config.php';

try {
    // Update admin records that have "Church Administrator" as full_name
    $stmt = $pdo->prepare("UPDATE admins SET full_name = 'Administrator' WHERE full_name = 'Church Administrator'");
    $result = $stmt->execute();
    
    if ($result) {
        $rowsAffected = $stmt->rowCount();
        echo "✅ Successfully updated $rowsAffected admin record(s) from 'Church Administrator' to 'Administrator'\n";
    } else {
        echo "❌ Failed to update admin records\n";
    }
    
    // Also update any other church-specific admin names
    $stmt2 = $pdo->prepare("UPDATE admins SET full_name = 'System Administrator' WHERE full_name = 'Church Admin'");
    $result2 = $stmt2->execute();
    
    if ($result2) {
        $rowsAffected2 = $stmt2->rowCount();
        echo "✅ Successfully updated $rowsAffected2 admin record(s) from 'Church Admin' to 'System Administrator'\n";
    }
    
    // Show current admin records
    echo "\n📋 Current admin records:\n";
    $stmt3 = $pdo->prepare("SELECT id, username, full_name, email FROM admins");
    $stmt3->execute();
    $admins = $stmt3->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($admins as $admin) {
        echo "ID: {$admin['id']}, Username: {$admin['username']}, Name: {$admin['full_name']}, Email: {$admin['email']}\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
