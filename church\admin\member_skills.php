<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config.php';

// Page title and header info
$page_title = 'Member Skills Management';
$page_header = 'Member Skills Management';
$page_description = 'Manage member skills and expertise';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_skill'])) {
            // Add new skill to member
            $stmt = $pdo->prepare("
                INSERT INTO member_skills (member_id, skill_name, proficiency_level, years_experience, description, verified)
                VALUES (?, ?, ?, ?, ?, 0)
            ");
            $stmt->execute([
                $_POST['member_id'],
                $_POST['skill_name'],
                $_POST['proficiency_level'],
                $_POST['years_experience'] ?: null,
                $_POST['description'] ?: null
            ]);
            $message = "Skill added successfully!";
        }
        
        if (isset($_POST['verify_skill'])) {
            // Verify a skill
            $stmt = $pdo->prepare("UPDATE member_skills SET verified = 1 WHERE id = ?");
            $stmt->execute([$_POST['skill_id']]);
            $message = "Skill verified successfully!";
        }
        
        if (isset($_POST['delete_skill'])) {
            // Delete a skill
            $stmt = $pdo->prepare("DELETE FROM member_skills WHERE id = ?");
            $stmt->execute([$_POST['skill_id']]);
            $message = "Skill deleted successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Create member_skills table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS `member_skills` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `skill_name` varchar(100) NOT NULL,
        `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
        `years_experience` int(11) DEFAULT NULL,
        `description` text DEFAULT NULL,
        `verified` tinyint(1) NOT NULL DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_member_skill` (`member_id`,`skill_name`),
        KEY `idx_skill_name` (`skill_name`),
        KEY `idx_proficiency` (`proficiency_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
} catch (PDOException $e) {
    // Table might already exist
}

// Get all member skills
try {
    $stmt = $pdo->prepare("
        SELECT ms.*, 
               COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), m.email) as member_name,
               m.email as member_email
        FROM member_skills ms
        LEFT JOIN members m ON ms.member_id = m.id
        ORDER BY ms.created_at DESC
    ");
    $stmt->execute();
    $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $skills = [];
    $error = "Error loading skills: " . $e->getMessage();
}

// Get all members for dropdown
try {
    $stmt = $pdo->prepare("
        SELECT id, 
               COALESCE(full_name, CONCAT(first_name, ' ', last_name), email) as full_name 
        FROM members 
        WHERE status = 'active' 
        ORDER BY full_name
    ");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $members = [];
}

// Get skill statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_skills,
            COUNT(DISTINCT member_id) as members_with_skills,
            COUNT(CASE WHEN verified = 1 THEN 1 END) as verified_skills,
            COUNT(CASE WHEN proficiency_level = 'expert' THEN 1 END) as expert_skills
        FROM member_skills
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = ['total_skills' => 0, 'members_with_skills' => 0, 'verified_skills' => 0, 'expert_skills' => 0];
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="col-md-9 col-lg-10 main-content">
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-tools fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['total_skills']; ?></h3>
                                    <small>Total Skills</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['members_with_skills']; ?></h3>
                                    <small>Skilled Members</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-patch-check fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['verified_skills']; ?></h3>
                                    <small>Verified Skills</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-star fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['expert_skills']; ?></h3>
                                    <small>Expert Level</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add New Skill -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-plus-circle"></i> Add New Skill</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="member_id" class="form-label">Member</label>
                                <select name="member_id" id="member_id" class="form-select" required>
                                    <option value="">Select Member</option>
                                    <?php foreach ($members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="skill_name" class="form-label">Skill Name</label>
                                <input type="text" name="skill_name" id="skill_name" class="form-control" required>
                            </div>
                            <div class="col-md-2">
                                <label for="proficiency_level" class="form-label">Proficiency</label>
                                <select name="proficiency_level" id="proficiency_level" class="form-select" required>
                                    <option value="beginner">Beginner</option>
                                    <option value="intermediate" selected>Intermediate</option>
                                    <option value="advanced">Advanced</option>
                                    <option value="expert">Expert</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="years_experience" class="form-label">Years Experience</label>
                                <input type="number" name="years_experience" id="years_experience" class="form-control" min="0" max="50">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" name="add_skill" class="btn btn-primary d-block w-100">Add Skill</button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label for="description" class="form-label">Description (Optional)</label>
                                <textarea name="description" id="description" class="form-control" rows="2" 
                                          placeholder="Additional details about this skill..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Skills List -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list-ul"></i> All Member Skills</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($skills)): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> No skills have been added yet. Use the form above to add member skills.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Skill</th>
                                        <th>Proficiency</th>
                                        <th>Experience</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($skills as $skill): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($skill['member_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($skill['member_email']); ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($skill['skill_name']); ?></strong>
                                                <?php if ($skill['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($skill['description']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $skill['proficiency_level'] == 'expert' ? 'danger' : 
                                                        ($skill['proficiency_level'] == 'advanced' ? 'warning' : 
                                                        ($skill['proficiency_level'] == 'intermediate' ? 'info' : 'secondary')); 
                                                ?>">
                                                    <?php echo ucfirst($skill['proficiency_level']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo $skill['years_experience'] ? $skill['years_experience'] . ' years' : 'Not specified'; ?>
                                            </td>
                                            <td>
                                                <?php if ($skill['verified']): ?>
                                                    <span class="badge bg-success">Verified</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Unverified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!$skill['verified']): ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                                        <button type="submit" name="verify_skill" class="btn btn-sm btn-success">
                                                            <i class="bi bi-check"></i> Verify
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <form method="POST" class="d-inline" onsubmit="return confirm('Delete this skill?')">
                                                    <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                                    <button type="submit" name="delete_skill" class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
