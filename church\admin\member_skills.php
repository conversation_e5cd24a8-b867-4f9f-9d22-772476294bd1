<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config.php';

// Page title and header info
$page_title = 'Member Skills Management';
$page_header = 'Member Skills Management';
$page_description = 'Manage member skills and expertise';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_skill'])) {
            // Add new skill to member
            $stmt = $pdo->prepare("
                INSERT INTO member_skills (member_id, skill_id, proficiency_level, years_experience, notes, willing_to_teach, willing_to_volunteer)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $_POST['member_id'],
                $_POST['skill_id'],
                $_POST['proficiency_level'],
                $_POST['years_experience'] ?: null,
                $_POST['notes'] ?: null,
                isset($_POST['willing_to_teach']) ? 1 : 0,
                isset($_POST['willing_to_volunteer']) ? 1 : 0
            ]);
            $message = "Skill added successfully!";
        }

        if (isset($_POST['verify_skill'])) {
            // Verify a skill
            $stmt = $pdo->prepare("UPDATE member_skills SET verified_by = ?, verified_at = NOW() WHERE id = ?");
            $stmt->execute([$_SESSION['admin_id'], $_POST['skill_id']]);
            $message = "Skill verified successfully!";
        }
        
        if (isset($_POST['delete_skill'])) {
            // Delete a skill
            $stmt = $pdo->prepare("DELETE FROM member_skills WHERE id = ?");
            $stmt->execute([$_POST['skill_id']]);
            $message = "Skill deleted successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Create skills_catalog table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS `skills_catalog` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `skill_name` varchar(255) NOT NULL,
        `skill_category` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_by` int(11) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_skill_name` (`skill_name`),
        KEY `idx_skill_category` (`skill_category`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
} catch (PDOException $e) {
    // Table might already exist
}

// Create member_skills table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS `member_skills` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `skill_id` int(11) NOT NULL,
        `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
        `years_experience` int(11) DEFAULT NULL,
        `willing_to_teach` tinyint(1) NOT NULL DEFAULT 0,
        `willing_to_volunteer` tinyint(1) NOT NULL DEFAULT 1,
        `availability` text DEFAULT NULL,
        `notes` text DEFAULT NULL,
        `verified_by` int(11) DEFAULT NULL,
        `verified_at` datetime DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_member_skill` (`member_id`, `skill_id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_skill_id` (`skill_id`),
        KEY `idx_proficiency` (`proficiency_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
} catch (PDOException $e) {
    // Table might already exist
}

// Add some default skills if skills_catalog is empty
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM skills_catalog");
    $count = $stmt->fetch()['count'];

    if ($count == 0) {
        $defaultSkills = [
            ['Public Speaking', 'Communication', 'Ability to speak confidently in front of groups'],
            ['Music - Vocals', 'Music & Arts', 'Singing and vocal performance'],
            ['Music - Piano', 'Music & Arts', 'Piano playing skills'],
            ['Music - Guitar', 'Music & Arts', 'Guitar playing skills'],
            ['Teaching', 'Education', 'Ability to teach and educate others'],
            ['Childcare', 'Care & Support', 'Experience caring for children'],
            ['Event Planning', 'Organization', 'Planning and coordinating events'],
            ['Photography', 'Media & Technology', 'Photography and visual media'],
            ['Audio/Visual', 'Media & Technology', 'Sound and video equipment operation'],
            ['Cooking', 'Hospitality', 'Food preparation and cooking'],
            ['Cleaning', 'Maintenance', 'Cleaning and maintenance tasks'],
            ['Administration', 'Organization', 'Administrative and office tasks']
        ];

        $stmt = $pdo->prepare("INSERT INTO skills_catalog (skill_name, skill_category, description, created_by) VALUES (?, ?, ?, 1)");
        foreach ($defaultSkills as $skill) {
            $stmt->execute($skill);
        }
    }
} catch (PDOException $e) {
    // Ignore errors
}

// Get all member skills
try {
    $stmt = $pdo->prepare("
        SELECT ms.*,
               COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), m.email) as member_name,
               m.email as member_email,
               sc.skill_name,
               sc.skill_category,
               sc.description as skill_description
        FROM member_skills ms
        LEFT JOIN members m ON ms.member_id = m.id
        LEFT JOIN skills_catalog sc ON ms.skill_id = sc.id
        ORDER BY ms.created_at DESC
    ");
    $stmt->execute();
    $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $skills = [];
    $error = "Error loading skills: " . $e->getMessage();
}

// Get all skills for dropdown
try {
    $stmt = $pdo->prepare("SELECT id, skill_name, skill_category FROM skills_catalog WHERE is_active = 1 ORDER BY skill_category, skill_name");
    $stmt->execute();
    $availableSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $availableSkills = [];
}

// Get all members for dropdown
try {
    $stmt = $pdo->prepare("
        SELECT id, 
               COALESCE(full_name, CONCAT(first_name, ' ', last_name), email) as full_name 
        FROM members 
        WHERE status = 'active' 
        ORDER BY full_name
    ");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $members = [];
}

// Get skill statistics
try {
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_skills,
            COUNT(DISTINCT member_id) as members_with_skills,
            COUNT(CASE WHEN verified_by IS NOT NULL THEN 1 END) as verified_skills,
            COUNT(CASE WHEN proficiency_level = 'expert' THEN 1 END) as expert_skills
        FROM member_skills
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = ['total_skills' => 0, 'members_with_skills' => 0, 'verified_skills' => 0, 'expert_skills' => 0];
}

// Include header
include 'includes/header.php';
?>

<style>
/* Fix modal z-index to appear above sidebar */
.modal {
    z-index: 1060 !important;
}
.modal-backdrop {
    z-index: 1055 !important;
}
</style>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-tools fs-1"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?php echo $stats['total_skills']; ?></h3>
                        <small>Total Skills</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?php echo $stats['members_with_skills']; ?></h3>
                        <small>Skilled Members</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-patch-check fs-1"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?php echo $stats['verified_skills']; ?></h3>
                        <small>Verified Skills</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-star fs-1"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?php echo $stats['expert_skills']; ?></h3>
                        <small>Expert Level</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


            <!-- Add New Skill -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-plus-circle"></i> Add New Skill</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="member_id" class="form-label">Member</label>
                                <select name="member_id" id="member_id" class="form-select" required>
                                    <option value="">Select Member</option>
                                    <?php foreach ($members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="skill_id" class="form-label">Skill</label>
                                <select name="skill_id" id="skill_id" class="form-select" required>
                                    <option value="">Select Skill</option>
                                    <?php
                                    $currentCategory = '';
                                    foreach ($availableSkills as $skill):
                                        if ($skill['skill_category'] != $currentCategory) {
                                            if ($currentCategory != '') echo '</optgroup>';
                                            echo '<optgroup label="' . htmlspecialchars($skill['skill_category']) . '">';
                                            $currentCategory = $skill['skill_category'];
                                        }
                                    ?>
                                        <option value="<?php echo $skill['id']; ?>">
                                            <?php echo htmlspecialchars($skill['skill_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <?php if ($currentCategory != '') echo '</optgroup>'; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="proficiency_level" class="form-label">Proficiency</label>
                                <select name="proficiency_level" id="proficiency_level" class="form-select" required>
                                    <option value="beginner">Beginner</option>
                                    <option value="intermediate" selected>Intermediate</option>
                                    <option value="advanced">Advanced</option>
                                    <option value="expert">Expert</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="years_experience" class="form-label">Years Experience</label>
                                <input type="number" name="years_experience" id="years_experience" class="form-control" min="0" max="50">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" name="add_skill" class="btn btn-primary d-block w-100">Add Skill</button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="notes" class="form-label">Notes (Optional)</label>
                                <textarea name="notes" id="notes" class="form-control" rows="2"
                                          placeholder="Additional details about this skill..."></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Availability</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="willing_to_teach" id="willing_to_teach" value="1">
                                    <label class="form-check-label" for="willing_to_teach">
                                        Willing to teach this skill
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="willing_to_volunteer" id="willing_to_volunteer" value="1" checked>
                                    <label class="form-check-label" for="willing_to_volunteer">
                                        Willing to volunteer using this skill
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Skills List -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list-ul"></i> All Member Skills</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($skills)): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> No skills have been added yet. Use the form above to add member skills.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Skill</th>
                                        <th>Proficiency</th>
                                        <th>Experience</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($skills as $skill): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($skill['member_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($skill['member_email']); ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($skill['skill_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($skill['skill_category']); ?></small>
                                                <?php if ($skill['notes']): ?>
                                                    <br><small class="text-info"><?php echo htmlspecialchars($skill['notes']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $skill['proficiency_level'] == 'expert' ? 'danger' : 
                                                        ($skill['proficiency_level'] == 'advanced' ? 'warning' : 
                                                        ($skill['proficiency_level'] == 'intermediate' ? 'info' : 'secondary')); 
                                                ?>">
                                                    <?php echo ucfirst($skill['proficiency_level']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo $skill['years_experience'] ? $skill['years_experience'] . ' years' : 'Not specified'; ?>
                                            </td>
                                            <td>
                                                <?php if ($skill['verified_by']): ?>
                                                    <span class="badge bg-success">Verified</span>
                                                    <br><small class="text-muted">on <?php echo date('M j, Y', strtotime($skill['verified_at'])); ?></small>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Unverified</span>
                                                <?php endif; ?>
                                                <?php if ($skill['willing_to_teach']): ?>
                                                    <br><span class="badge bg-info">Willing to Teach</span>
                                                <?php endif; ?>
                                                <?php if ($skill['willing_to_volunteer']): ?>
                                                    <br><span class="badge bg-primary">Available to Volunteer</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!$skill['verified_by']): ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                                        <button type="submit" name="verify_skill" class="btn btn-sm btn-success">
                                                            <i class="bi bi-check"></i> Verify
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <form method="POST" class="d-inline" onsubmit="return confirm('Delete this skill?')">
                                                    <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                                    <button type="submit" name="delete_skill" class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
