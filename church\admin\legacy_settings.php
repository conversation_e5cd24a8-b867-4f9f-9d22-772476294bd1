<?php
session_start();
require_once "../config.php";

// Include language system
require_once 'includes/language.php';

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        $pdo->beginTransaction();

        // Collect all settings from form
        $allSettings = [];

        // General Settings
        if (isset($_POST['general'])) {
            $allSettings = array_merge($allSettings, [
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'site_description' => $_POST['site_description'] ?? '',
                'site_keywords' => $_POST['site_keywords'] ?? '',
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_name' => $_POST['organization_name'] ?? '',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'footer_text' => $_POST['footer_text'] ?? ''
            ]);
        }

        // Contact Information
        if (isset($_POST['contact'])) {
            $allSettings = array_merge($allSettings, [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'office_hours' => $_POST['office_hours'] ?? '',
                'emergency_contact' => $_POST['emergency_contact'] ?? ''
            ]);
        }

        // Social Media
        if (isset($_POST['social'])) {
            $allSettings = array_merge($allSettings, [
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'linkedin_url' => $_POST['linkedin_url'] ?? '',
                'tiktok_url' => $_POST['tiktok_url'] ?? '',
                'website_url' => $_POST['website_url'] ?? '',
                'blog_url' => $_POST['blog_url'] ?? ''
            ]);
        }

        // Email Configuration
        if (isset($_POST['email'])) {
            $allSettings = array_merge($allSettings, [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'from_email' => $_POST['from_email'] ?? '',
                'from_name' => $_POST['from_name'] ?? '',
                'reply_to_email' => $_POST['reply_to_email'] ?? '',
                'email_signature' => $_POST['email_signature'] ?? '',
                'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
            ]);
        }

        // System Preferences
        if (isset($_POST['system'])) {
            $allSettings = array_merge($allSettings, [
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                'currency_code' => $_POST['currency_code'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'items_per_page' => $_POST['items_per_page'] ?? '25',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
            ]);
        }

        // Notification Settings
        if (isset($_POST['notifications'])) {
            $allSettings = array_merge($allSettings, [
                'enable_event_reminders' => isset($_POST['enable_event_reminders']) ? '1' : '0',
                'event_reminder_days' => $_POST['event_reminder_days'] ?? '3',
                'enable_membership_expiry_alerts' => isset($_POST['enable_membership_expiry_alerts']) ? '1' : '0',
                'membership_expiry_days' => $_POST['membership_expiry_days'] ?? '30',
                'enable_admin_notifications' => isset($_POST['enable_admin_notifications']) ? '1' : '0',
                'admin_notification_email' => $_POST['admin_notification_email'] ?? '',
                'notification_frequency' => $_POST['notification_frequency'] ?? 'daily',
                'birthday_notification_days' => $_POST['birthday_notification_days'] ?? '7'
            ]);
        }

        // Integration Settings
        if (isset($_POST['integrations'])) {
            $allSettings = array_merge($allSettings, [
                'google_analytics_id' => $_POST['google_analytics_id'] ?? '',
                'facebook_pixel_id' => $_POST['facebook_pixel_id'] ?? '',
                'google_maps_api_key' => $_POST['google_maps_api_key'] ?? '',
                'whatsapp_api_token' => $_POST['whatsapp_api_token'] ?? '',
                'sms_api_key' => $_POST['sms_api_key'] ?? '',
                'payment_gateway' => $_POST['payment_gateway'] ?? 'stripe',
                'stripe_public_key' => $_POST['stripe_public_key'] ?? '',
                'stripe_secret_key' => $_POST['stripe_secret_key'] ?? '',
                'paypal_client_id' => $_POST['paypal_client_id'] ?? '',
                'enable_api_access' => isset($_POST['enable_api_access']) ? '1' : '0'
            ]);
        }

        // Save all settings to unified settings table
        foreach ($allSettings as $key => $value) {
            update_site_setting($key, $value);
        }

        $pdo->commit();
        $success = __('settings_updated_successfully');

    } catch (Exception $e) {
        $pdo->rollback();
        $error = __('error') . ": " . $e->getMessage();
    }
}

// Include shared settings functions
require_once 'includes/settings_functions.php';

$currentSettings = getCurrentSettings();

// Page title and header info
$pageTitle = __('general_settings');
include "includes/header.php";
?>

        <div class="container-fluid">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-gear"></i> <?php _e('general_settings'); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security_settings.php" class="btn btn-outline-secondary">
                            <i class="bi bi-shield-lock"></i> <?php _e('security_settings'); ?>
                        </a>
                        <a href="automated_email_templates.php" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope-gear"></i> <?php _e('email_templates'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Settings Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                        <i class="bi bi-house"></i> <?php _e('general'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                        <i class="bi bi-telephone"></i> <?php _e('contact'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                        <i class="bi bi-share"></i> <?php _e('social_media'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                        <i class="bi bi-envelope"></i> <?php _e('email'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-cpu"></i> <?php _e('system'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell"></i> <?php _e('notifications'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
                        <i class="bi bi-puzzle"></i> <?php _e('integrations'); ?>
                    </button>
                </li>
            </ul>

            <form method="POST" action="">
                <div class="tab-content" id="settingsTabContent">

                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-house"></i> <?php _e('general_settings'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_title" class="form-label"><?php _e('site_title'); ?></label>
                                            <input type="text" class="form-control" id="site_title" name="site_title"
                                                   value="<?php echo htmlspecialchars($currentSettings['site_title']); ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="admin_title" class="form-label"><?php _e('admin_panel_title'); ?></label>
                                            <input type="text" class="form-control" id="admin_title" name="admin_title"
                                                   value="<?php echo htmlspecialchars($currentSettings['admin_title']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="organization_type" class="form-label"><?php _e('organization_type'); ?></label>
                                            <select class="form-select" id="organization_type" name="organization_type">
                                                <option value="church" <?php echo $currentSettings['organization_type'] === 'church' ? 'selected' : ''; ?>><?php _e('church'); ?></option>
                                                <option value="nonprofit" <?php echo $currentSettings['organization_type'] === 'nonprofit' ? 'selected' : ''; ?>><?php _e('nonprofit'); ?></option>
                                                <option value="ministry" <?php echo $currentSettings['organization_type'] === 'ministry' ? 'selected' : ''; ?>><?php _e('ministry'); ?></option>
                                                <option value="community" <?php echo $currentSettings['organization_type'] === 'community' ? 'selected' : ''; ?>><?php _e('community_group'); ?></option>
                                                <option value="other" <?php echo $currentSettings['organization_type'] === 'other' ? 'selected' : ''; ?>><?php _e('other'); ?></option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="organization_name" class="form-label"><?php _e('organization_name'); ?></label>
                                            <input type="text" class="form-control" id="organization_name" name="organization_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['organization_name']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_description" class="form-label"><?php _e('site_description'); ?></label>
                                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($currentSettings['site_description']); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="site_keywords" class="form-label"><?php _e('site_keywords'); ?></label>
                                            <input type="text" class="form-control" id="site_keywords" name="site_keywords"
                                                   value="<?php echo htmlspecialchars($currentSettings['site_keywords']); ?>"
                                                   placeholder="keyword1, keyword2, keyword3">
                                        </div>
                                        <div class="mb-3">
                                            <label for="footer_text" class="form-label"><?php _e('footer_text'); ?></label>
                                            <textarea class="form-control" id="footer_text" name="footer_text" rows="2"><?php echo htmlspecialchars($currentSettings['footer_text']); ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <hr>
                                <h6><i class="bi bi-tags"></i> <?php _e('organization_terminology'); ?></h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="member_term" class="form-label"><?php _e('member_term'); ?></label>
                                            <input type="text" class="form-control" id="member_term" name="member_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['member_term']); ?>" placeholder="Member">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="leader_term" class="form-label"><?php _e('leader_term'); ?></label>
                                            <input type="text" class="form-control" id="leader_term" name="leader_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['leader_term']); ?>" placeholder="Pastor">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="group_term" class="form-label"><?php _e('group_term'); ?></label>
                                            <input type="text" class="form-control" id="group_term" name="group_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['group_term']); ?>" placeholder="Ministry">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="event_term" class="form-label"><?php _e('event_term'); ?></label>
                                            <input type="text" class="form-control" id="event_term" name="event_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['event_term']); ?>" placeholder="Service">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="donation_term" class="form-label"><?php _e('donation_term'); ?></label>
                                            <input type="text" class="form-control" id="donation_term" name="donation_term"
                                                   value="<?php echo htmlspecialchars($currentSettings['donation_term']); ?>" placeholder="Offering">
                                        </div>
                                    </div>
                                </div>

                                <hr>
                                <h6><i class="bi bi-card-text"></i> <?php _e('mission_vision'); ?></h6>
                                <div class="mb-3">
                                    <label for="organization_mission" class="form-label"><?php _e('mission_statement'); ?></label>
                                    <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($currentSettings['organization_mission']); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="organization_vision" class="form-label"><?php _e('vision_statement'); ?></label>
                                    <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($currentSettings['organization_vision']); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="organization_values" class="form-label"><?php _e('core_values'); ?></label>
                                    <textarea class="form-control" id="organization_values" name="organization_values" rows="4"><?php echo htmlspecialchars($currentSettings['organization_values']); ?></textarea>
                                </div>

                                <button type="submit" name="general" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_general_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Tab -->
                    <div class="tab-pane fade" id="contact" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-telephone"></i> <?php _e('contact_information'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_phone" class="form-label"><?php _e('phone_number'); ?></label>
                                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="contact_email" class="form-label"><?php _e('contact_email'); ?></label>
                                            <input type="email" class="form-control" id="contact_email" name="contact_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="emergency_contact" class="form-label"><?php _e('emergency_contact'); ?></label>
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                                   value="<?php echo htmlspecialchars($currentSettings['emergency_contact']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_address" class="form-label"><?php _e('street_address'); ?></label>
                                            <input type="text" class="form-control" id="contact_address" name="contact_address"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_address']); ?>">
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_city" class="form-label"><?php _e('city'); ?></label>
                                                    <input type="text" class="form-control" id="contact_city" name="contact_city"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_city']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_state" class="form-label"><?php _e('state_province'); ?></label>
                                                    <input type="text" class="form-control" id="contact_state" name="contact_state"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_state']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_zip" class="form-label"><?php _e('zip_postal_code'); ?></label>
                                                    <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_zip']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contact_country" class="form-label"><?php _e('country'); ?></label>
                                                    <input type="text" class="form-control" id="contact_country" name="contact_country"
                                                           value="<?php echo htmlspecialchars($currentSettings['contact_country']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="office_hours" class="form-label"><?php _e('office_hours'); ?></label>
                                    <textarea class="form-control" id="office_hours" name="office_hours" rows="3"
                                              placeholder="Monday - Friday: 9:00 AM - 5:00 PM"><?php echo htmlspecialchars($currentSettings['office_hours']); ?></textarea>
                                </div>

                                <button type="submit" name="contact" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_contact_information'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-share"></i> <?php _e('social_media_links'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook_url" class="form-label">
                                                <i class="bi bi-facebook"></i> <?php _e('facebook_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>"
                                                   placeholder="https://facebook.com/yourpage">
                                        </div>
                                        <div class="mb-3">
                                            <label for="twitter_url" class="form-label">
                                                <i class="bi bi-twitter"></i> <?php _e('twitter_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>"
                                                   placeholder="https://twitter.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="instagram_url" class="form-label">
                                                <i class="bi bi-instagram"></i> <?php _e('instagram_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>"
                                                   placeholder="https://instagram.com/yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="youtube_url" class="form-label">
                                                <i class="bi bi-youtube"></i> <?php _e('youtube_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>"
                                                   placeholder="https://youtube.com/yourchannel">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="linkedin_url" class="form-label">
                                                <i class="bi bi-linkedin"></i> <?php _e('linkedin_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>"
                                                   placeholder="https://linkedin.com/company/yourcompany">
                                        </div>
                                        <div class="mb-3">
                                            <label for="tiktok_url" class="form-label">
                                                <i class="bi bi-tiktok"></i> <?php _e('tiktok_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['tiktok_url']); ?>"
                                                   placeholder="https://tiktok.com/@yourhandle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="website_url" class="form-label">
                                                <i class="bi bi-globe"></i> <?php _e('website_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="website_url" name="website_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>"
                                                   placeholder="https://yourwebsite.com">
                                        </div>
                                        <div class="mb-3">
                                            <label for="blog_url" class="form-label">
                                                <i class="bi bi-journal-text"></i> <?php _e('blog_url'); ?>
                                            </label>
                                            <input type="url" class="form-control" id="blog_url" name="blog_url"
                                                   value="<?php echo htmlspecialchars($currentSettings['blog_url']); ?>"
                                                   placeholder="https://yourblog.com">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="social" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_social_media_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration Tab -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> <?php _e('email_configuration'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtp_host" class="form-label"><?php _e('smtp_host'); ?></label>
                                            <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_port" class="form-label"><?php _e('smtp_port'); ?></label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_port']); ?>"
                                                   placeholder="587">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_encryption" class="form-label"><?php _e('encryption'); ?></label>
                                            <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                                <option value="tls" <?php echo $currentSettings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo $currentSettings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                <option value="none" <?php echo $currentSettings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>><?php _e('none'); ?></option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_username" class="form-label"><?php _e('smtp_username'); ?></label>
                                            <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_password" class="form-label"><?php _e('smtp_password'); ?></label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="from_email" class="form-label"><?php _e('from_email'); ?></label>
                                            <input type="email" class="form-control" id="from_email" name="from_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['from_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="from_name" class="form-label"><?php _e('from_name'); ?></label>
                                            <input type="text" class="form-control" id="from_name" name="from_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['from_name']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="reply_to_email" class="form-label"><?php _e('reply_to_email'); ?></label>
                                            <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['reply_to_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="email_signature" class="form-label"><?php _e('email_signature'); ?></label>
                                            <textarea class="form-control" id="email_signature" name="email_signature" rows="3"><?php echo htmlspecialchars($currentSettings['email_signature']); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                                       <?php echo $currentSettings['enable_email_queue'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_email_queue">
                                                    <?php _e('enable_email_queue'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="email" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_email_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- System Preferences Tab -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-cpu"></i> <?php _e('system_preferences'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label"><?php _e('timezone'); ?></label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="America/New_York" <?php echo $currentSettings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>><?php _e('eastern_time'); ?></option>
                                                <option value="America/Chicago" <?php echo $currentSettings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>><?php _e('central_time'); ?></option>
                                                <option value="America/Denver" <?php echo $currentSettings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>><?php _e('mountain_time'); ?></option>
                                                <option value="America/Los_Angeles" <?php echo $currentSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>><?php _e('pacific_time'); ?></option>
                                                <option value="UTC" <?php echo $currentSettings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="date_format" class="form-label"><?php _e('date_format'); ?></label>
                                            <select class="form-select" id="date_format" name="date_format">
                                                <option value="Y-m-d" <?php echo $currentSettings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                                <option value="m/d/Y" <?php echo $currentSettings['date_format'] === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                                <option value="d/m/Y" <?php echo $currentSettings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                                <option value="F j, Y" <?php echo $currentSettings['date_format'] === 'F j, Y' ? 'selected' : ''; ?>>Month DD, YYYY</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="time_format" class="form-label"><?php _e('time_format'); ?></label>
                                            <select class="form-select" id="time_format" name="time_format">
                                                <option value="H:i" <?php echo $currentSettings['time_format'] === 'H:i' ? 'selected' : ''; ?>>24-hour (HH:MM)</option>
                                                <option value="g:i A" <?php echo $currentSettings['time_format'] === 'g:i A' ? 'selected' : ''; ?>>12-hour (H:MM AM/PM)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="currency_symbol" class="form-label"><?php _e('currency_symbol'); ?></label>
                                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                                   value="<?php echo htmlspecialchars($currentSettings['currency_symbol']); ?>" maxlength="5">
                                        </div>
                                        <div class="mb-3">
                                            <label for="currency_code" class="form-label"><?php _e('currency_code'); ?></label>
                                            <input type="text" class="form-control" id="currency_code" name="currency_code"
                                                   value="<?php echo htmlspecialchars($currentSettings['currency_code']); ?>" maxlength="3">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="language" class="form-label"><?php _e('language'); ?></label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="en" <?php echo $currentSettings['language'] === 'en' ? 'selected' : ''; ?>><?php _e('english'); ?></option>
                                                <option value="es" <?php echo $currentSettings['language'] === 'es' ? 'selected' : ''; ?>><?php _e('spanish'); ?></option>
                                                <option value="fr" <?php echo $currentSettings['language'] === 'fr' ? 'selected' : ''; ?>><?php _e('french'); ?></option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="items_per_page" class="form-label"><?php _e('items_per_page'); ?></label>
                                            <select class="form-select" id="items_per_page" name="items_per_page">
                                                <option value="10" <?php echo $currentSettings['items_per_page'] === '10' ? 'selected' : ''; ?>>10</option>
                                                <option value="25" <?php echo $currentSettings['items_per_page'] === '25' ? 'selected' : ''; ?>>25</option>
                                                <option value="50" <?php echo $currentSettings['items_per_page'] === '50' ? 'selected' : ''; ?>>50</option>
                                                <option value="100" <?php echo $currentSettings['items_per_page'] === '100' ? 'selected' : ''; ?>>100</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="session_timeout" class="form-label"><?php _e('session_timeout_seconds'); ?></label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                   value="<?php echo htmlspecialchars($currentSettings['session_timeout']); ?>" min="300" max="86400">
                                        </div>
                                        <div class="mb-3">
                                            <label for="max_upload_size" class="form-label"><?php _e('max_upload_size_mb'); ?></label>
                                            <input type="number" class="form-control" id="max_upload_size" name="max_upload_size"
                                                   value="<?php echo htmlspecialchars($currentSettings['max_upload_size']); ?>" min="1" max="100">
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_retention_days" class="form-label"><?php _e('backup_retention_days'); ?></label>
                                            <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['backup_retention_days']); ?>" min="1" max="365">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="system" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_system_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-bell"></i> <?php _e('notification_settings'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_event_reminders" name="enable_event_reminders"
                                                       <?php echo $currentSettings['enable_event_reminders'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_event_reminders">
                                                    <?php _e('enable_event_reminders'); ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="event_reminder_days" class="form-label"><?php _e('event_reminder_days'); ?></label>
                                            <input type="number" class="form-control" id="event_reminder_days" name="event_reminder_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['event_reminder_days']); ?>" min="1" max="30">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_membership_expiry_alerts" name="enable_membership_expiry_alerts"
                                                       <?php echo $currentSettings['enable_membership_expiry_alerts'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_membership_expiry_alerts">
                                                    <?php _e('enable_membership_expiry_alerts'); ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="membership_expiry_days" class="form-label"><?php _e('membership_expiry_days'); ?></label>
                                            <input type="number" class="form-control" id="membership_expiry_days" name="membership_expiry_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['membership_expiry_days']); ?>" min="1" max="365">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_admin_notifications" name="enable_admin_notifications"
                                                       <?php echo $currentSettings['enable_admin_notifications'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_admin_notifications">
                                                    <?php _e('enable_admin_notifications'); ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="admin_notification_email" class="form-label"><?php _e('admin_notification_email'); ?></label>
                                            <input type="email" class="form-control" id="admin_notification_email" name="admin_notification_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['admin_notification_email']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="notification_frequency" class="form-label"><?php _e('notification_frequency'); ?></label>
                                            <select class="form-select" id="notification_frequency" name="notification_frequency">
                                                <option value="immediate" <?php echo $currentSettings['notification_frequency'] === 'immediate' ? 'selected' : ''; ?>><?php _e('immediate'); ?></option>
                                                <option value="daily" <?php echo $currentSettings['notification_frequency'] === 'daily' ? 'selected' : ''; ?>><?php _e('daily'); ?></option>
                                                <option value="weekly" <?php echo $currentSettings['notification_frequency'] === 'weekly' ? 'selected' : ''; ?>><?php _e('weekly'); ?></option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="birthday_notification_days" class="form-label"><?php _e('birthday_notification_days'); ?></label>
                                            <input type="number" class="form-control" id="birthday_notification_days" name="birthday_notification_days"
                                                   value="<?php echo htmlspecialchars($currentSettings['birthday_notification_days']); ?>" min="1" max="30">
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="notifications" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_notification_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Integration Settings Tab -->
                    <div class="tab-pane fade" id="integrations" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-puzzle"></i> <?php _e('integration_settings'); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="google_analytics_id" class="form-label"><?php _e('google_analytics_id'); ?></label>
                                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['google_analytics_id']); ?>"
                                                   placeholder="GA-XXXXXXXXX-X">
                                        </div>
                                        <div class="mb-3">
                                            <label for="facebook_pixel_id" class="form-label"><?php _e('facebook_pixel_id'); ?></label>
                                            <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['facebook_pixel_id']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="google_maps_api_key" class="form-label"><?php _e('google_maps_api_key'); ?></label>
                                            <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['google_maps_api_key']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="whatsapp_api_token" class="form-label"><?php _e('whatsapp_api_token'); ?></label>
                                            <input type="text" class="form-control" id="whatsapp_api_token" name="whatsapp_api_token"
                                                   value="<?php echo htmlspecialchars($currentSettings['whatsapp_api_token']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="sms_api_key" class="form-label"><?php _e('sms_api_key'); ?></label>
                                            <input type="text" class="form-control" id="sms_api_key" name="sms_api_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['sms_api_key']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_gateway" class="form-label"><?php _e('payment_gateway'); ?></label>
                                            <select class="form-select" id="payment_gateway" name="payment_gateway">
                                                <option value="stripe" <?php echo $currentSettings['payment_gateway'] === 'stripe' ? 'selected' : ''; ?>>Stripe</option>
                                                <option value="paypal" <?php echo $currentSettings['payment_gateway'] === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                                                <option value="both" <?php echo $currentSettings['payment_gateway'] === 'both' ? 'selected' : ''; ?>><?php _e('both'); ?></option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="stripe_public_key" class="form-label"><?php _e('stripe_public_key'); ?></label>
                                            <input type="text" class="form-control" id="stripe_public_key" name="stripe_public_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['stripe_public_key']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="stripe_secret_key" class="form-label"><?php _e('stripe_secret_key'); ?></label>
                                            <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key"
                                                   value="<?php echo htmlspecialchars($currentSettings['stripe_secret_key']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label for="paypal_client_id" class="form-label"><?php _e('paypal_client_id'); ?></label>
                                            <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id"
                                                   value="<?php echo htmlspecialchars($currentSettings['paypal_client_id']); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_api_access" name="enable_api_access"
                                                       <?php echo $currentSettings['enable_api_access'] === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="enable_api_access">
                                                    <?php _e('enable_api_access'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" name="integrations" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php _e('save_integration_settings'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Links to related settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-link-45deg"></i> <?php _e('related_settings'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="security_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-shield-lock"></i> <?php _e('security_settings'); ?>
                                    <small class="d-block text-muted"><?php _e('security_settings_description'); ?></small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="appearance_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-palette"></i> <?php _e('appearance_settings'); ?>
                                    <small class="d-block text-muted"><?php _e('appearance_settings_description'); ?></small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="automated_email_templates.php" class="btn btn-outline-primary">
                                    <i class="bi bi-envelope-gear"></i> <?php _e('email_templates'); ?>
                                    <small class="d-block text-muted"><?php _e('email_templates_description'); ?></small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
