<?php
/**
 * Advanced Features Database Migration - Phase 2
 * Family relationships, prayer requests, skills/volunteer matching, and member portal enhancements
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$migration_log = [];
$errors = [];

function log_migration($message, $type = 'info') {
    global $migration_log;
    $migration_log[] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
}

function execute_sql($pdo, $sql, $description) {
    global $errors;
    try {
        $pdo->exec($sql);
        log_migration("✓ $description", 'success');
        return true;
    } catch (PDOException $e) {
        $error_msg = "✗ $description - Error: " . $e->getMessage();
        log_migration($error_msg, 'error');
        $errors[] = $error_msg;
        return false;
    }
}

function column_exists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function table_exists($pdo, $table) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    log_migration("Starting Advanced Features Database Migration - Phase 2", 'info');
    
    try {
        $pdo->beginTransaction();
        
        // ============================================================================
        // FAMILY RELATIONSHIPS
        // ============================================================================
        
        if (!table_exists($pdo, 'family_relationships')) {
            $sql = "CREATE TABLE `family_relationships` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `primary_member_id` int(11) NOT NULL COMMENT 'Main family member account',
              `related_member_id` int(11) NOT NULL COMMENT 'Family member being linked',
              `relationship_type` enum('spouse','child','parent','sibling','grandparent','grandchild','other') NOT NULL,
              `relationship_description` varchar(100) DEFAULT NULL,
              `can_manage_profile` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can edit related member profile',
              `can_rsvp_for` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can RSVP on behalf of related member',
              `is_emergency_contact` tinyint(1) NOT NULL DEFAULT 0,
              `created_by` int(11) NOT NULL,
              `approved_by_related` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Approved by the related member',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_family_relationship` (`primary_member_id`, `related_member_id`),
              KEY `idx_primary_member` (`primary_member_id`),
              KEY `idx_related_member` (`related_member_id`),
              KEY `idx_relationship_type` (`relationship_type`),
              FOREIGN KEY (`primary_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`related_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`created_by`) REFERENCES `members` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created family_relationships table");
        }
        
        if (!table_exists($pdo, 'family_groups')) {
            $sql = "CREATE TABLE `family_groups` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `family_name` varchar(255) NOT NULL,
              `head_of_family_id` int(11) NOT NULL,
              `family_address` text DEFAULT NULL,
              `family_phone` varchar(20) DEFAULT NULL,
              `family_email` varchar(255) DEFAULT NULL,
              `anniversary_date` date DEFAULT NULL,
              `family_notes` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_head_of_family` (`head_of_family_id`),
              FOREIGN KEY (`head_of_family_id`) REFERENCES `members` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created family_groups table");
        }
        
        if (!table_exists($pdo, 'family_group_members')) {
            $sql = "CREATE TABLE `family_group_members` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `family_group_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `role_in_family` varchar(100) DEFAULT NULL,
              `joined_date` date DEFAULT NULL,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_family_member` (`family_group_id`, `member_id`),
              KEY `idx_family_group` (`family_group_id`),
              KEY `idx_member` (`member_id`),
              FOREIGN KEY (`family_group_id`) REFERENCES `family_groups` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created family_group_members table");
        }
        
        // ============================================================================
        // PRAYER REQUEST SYSTEM
        // ============================================================================
        
        if (!table_exists($pdo, 'prayer_requests')) {
            $sql = "CREATE TABLE `prayer_requests` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `title` varchar(255) NOT NULL,
              `description` text NOT NULL,
              `category` enum('personal','family','health','work','ministry','community','other') NOT NULL DEFAULT 'personal',
              `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
              `privacy_level` enum('private','family','members','public') NOT NULL DEFAULT 'members',
              `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
              `status` enum('active','answered','closed') NOT NULL DEFAULT 'active',
              `answered_description` text DEFAULT NULL,
              `answered_date` datetime DEFAULT NULL,
              `expires_at` datetime DEFAULT NULL,
              `allow_comments` tinyint(1) NOT NULL DEFAULT 1,
              `notify_on_prayer` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_category` (`category`),
              KEY `idx_privacy_level` (`privacy_level`),
              KEY `idx_status` (`status`),
              KEY `idx_created_at` (`created_at`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created prayer_requests table");
        }
        
        if (!table_exists($pdo, 'prayer_responses')) {
            $sql = "CREATE TABLE `prayer_responses` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `prayer_request_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `response_type` enum('prayed','praying','will_pray') NOT NULL DEFAULT 'prayed',
              `comment` text DEFAULT NULL,
              `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_prayer_response` (`prayer_request_id`, `member_id`),
              KEY `idx_prayer_request` (`prayer_request_id`),
              KEY `idx_member_id` (`member_id`),
              FOREIGN KEY (`prayer_request_id`) REFERENCES `prayer_requests` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created prayer_responses table");
        }
        
        if (!table_exists($pdo, 'prayer_request_comments')) {
            $sql = "CREATE TABLE `prayer_request_comments` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `prayer_request_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `comment` text NOT NULL,
              `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
              `is_approved` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_prayer_request` (`prayer_request_id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_created_at` (`created_at`),
              FOREIGN KEY (`prayer_request_id`) REFERENCES `prayer_requests` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created prayer_request_comments table");
        }
        
        // ============================================================================
        // SKILLS & VOLUNTEER MATCHING
        // ============================================================================
        
        if (!table_exists($pdo, 'skills_catalog')) {
            $sql = "CREATE TABLE `skills_catalog` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `skill_name` varchar(255) NOT NULL,
              `skill_category` varchar(100) NOT NULL,
              `description` text DEFAULT NULL,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_by` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_skill_name` (`skill_name`),
              KEY `idx_skill_category` (`skill_category`),
              FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created skills_catalog table");
        }
        
        if (!table_exists($pdo, 'member_skills')) {
            $sql = "CREATE TABLE `member_skills` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `skill_id` int(11) NOT NULL,
              `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
              `years_experience` int(11) DEFAULT NULL,
              `willing_to_teach` tinyint(1) NOT NULL DEFAULT 0,
              `willing_to_volunteer` tinyint(1) NOT NULL DEFAULT 1,
              `availability` text DEFAULT NULL COMMENT 'When available to use this skill',
              `notes` text DEFAULT NULL,
              `verified_by` int(11) DEFAULT NULL COMMENT 'Admin who verified this skill',
              `verified_at` datetime DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_member_skill` (`member_id`, `skill_id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_skill_id` (`skill_id`),
              KEY `idx_proficiency` (`proficiency_level`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`skill_id`) REFERENCES `skills_catalog` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`verified_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created member_skills table");
        }
        
        $pdo->commit();
        log_migration("Phase 2 migration completed successfully!", 'success');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        log_migration("Migration failed: " . $e->getMessage(), 'error');
        $errors[] = "Migration failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Features Migration - Phase 2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-database-gear"></i> Advanced Features Migration - Phase 2</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($migration_log)): ?>
                            <div class="alert alert-info">
                                <h5><i class="bi bi-info-circle"></i> Phase 2 Migration</h5>
                                <p>This phase will add the remaining advanced features:</p>
                                <ul>
                                    <li><strong>Family Relationships</strong> - Link family members and manage relationships</li>
                                    <li><strong>Prayer Request System</strong> - Personal prayer tracking with privacy controls</li>
                                    <li><strong>Skills & Volunteer Matching</strong> - Member skills catalog and volunteer opportunities</li>
                                    <li><strong>Enhanced Member Portal</strong> - Achievements, celebrations, and activity feeds</li>
                                </ul>
                                <p class="mb-0"><strong>Note:</strong> Make sure Phase 1 migration was completed successfully first.</p>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    <i class="bi bi-play-circle"></i> Run Phase 2 Migration
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="migration-log">
                                <h5>Migration Log</h5>
                                <?php foreach ($migration_log as $entry): ?>
                                    <div class="alert alert-<?php echo $entry['type'] === 'error' ? 'danger' : ($entry['type'] === 'success' ? 'success' : 'info'); ?> py-2">
                                        <small class="text-muted"><?php echo $entry['timestamp']; ?></small><br>
                                        <?php echo htmlspecialchars($entry['message']); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($errors)): ?>
                                <div class="alert alert-success">
                                    <h5><i class="bi bi-check-circle"></i> Phase 2 Migration Completed!</h5>
                                    <p>All advanced features are now available. Next steps:</p>
                                    <ul>
                                        <li>Run Phase 3 migration for final enhancements</li>
                                        <li>Configure default skills and volunteer categories</li>
                                        <li>Set up reminder templates</li>
                                    </ul>
                                    <a href="migrate_advanced_features_phase3.php" class="btn btn-primary me-2">
                                        <i class="bi bi-arrow-right"></i> Continue to Phase 3
                                    </a>
                                    <a href="dashboard.php" class="btn btn-secondary">
                                        <i class="bi bi-house"></i> Return to Dashboard
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5><i class="bi bi-exclamation-triangle"></i> Migration Completed with Errors</h5>
                                    <p>Some parts of the migration failed. Please review the log above.</p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
