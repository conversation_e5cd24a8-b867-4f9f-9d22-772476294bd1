-- Advanced Event Management & Member Portal Features
-- Database Schema Enhancement
-- Created: 2025-07-02

-- ============================================================================
-- MULTI-SESSION EVENTS & WORKSHOPS
-- ============================================================================

-- Event Sessions Table (for multi-session events and workshops)
CREATE TABLE IF NOT EXISTS `event_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `session_title` varchar(255) NOT NULL,
  `session_description` text DEFAULT NULL,
  `session_number` int(11) NOT NULL DEFAULT 1,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `max_attendees` int(11) DEFAULT NULL,
  `instructor_name` varchar(255) DEFAULT NULL,
  `instructor_bio` text DEFAULT NULL,
  `session_type` enum('lecture','workshop','discussion','practical','break') NOT NULL DEFAULT 'lecture',
  `prerequisites` text DEFAULT NULL,
  `materials_needed` text DEFAULT NULL,
  `session_fee` decimal(10,2) DEFAULT 0.00,
  `is_mandatory` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_session_number` (`session_number`),
  KEY `idx_start_datetime` (`start_datetime`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Session RSVPs (separate from main event RSVP)
CREATE TABLE IF NOT EXISTS `session_rsvps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `response` enum('yes','no','maybe') NOT NULL DEFAULT 'yes',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_session_member` (`session_id`, `member_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_member_id` (`member_id`),
  FOREIGN KEY (`session_id`) REFERENCES `event_sessions` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- ENHANCED RSVP SYSTEM
-- ============================================================================

-- Guest Registration Details
CREATE TABLE IF NOT EXISTS `event_guests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `rsvp_id` int(11) DEFAULT NULL,
  `guest_name` varchar(255) NOT NULL,
  `guest_email` varchar(255) DEFAULT NULL,
  `guest_phone` varchar(20) DEFAULT NULL,
  `age_group` enum('child','teen','adult','senior') DEFAULT 'adult',
  `relationship_to_member` varchar(100) DEFAULT NULL,
  `dietary_restrictions` text DEFAULT NULL,
  `special_needs` text DEFAULT NULL,
  `emergency_contact_name` varchar(255) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_rsvp_id` (`rsvp_id`),
  KEY `idx_guest_email` (`guest_email`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced RSVP table (extending existing functionality)
ALTER TABLE `event_rsvps` 
ADD COLUMN `dietary_restrictions` text DEFAULT NULL COMMENT 'Dietary restrictions and allergies',
ADD COLUMN `special_needs` text DEFAULT NULL COMMENT 'Accessibility and special needs',
ADD COLUMN `transportation_needed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Needs transportation/carpooling',
ADD COLUMN `can_provide_transportation` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can provide rides to others',
ADD COLUMN `vehicle_capacity` int(11) DEFAULT NULL COMMENT 'Number of people can transport',
ADD COLUMN `emergency_contact_name` varchar(255) DEFAULT NULL,
ADD COLUMN `emergency_contact_phone` varchar(20) DEFAULT NULL,
ADD COLUMN `additional_info` text DEFAULT NULL COMMENT 'Any additional information';

-- Carpooling Coordination
CREATE TABLE IF NOT EXISTS `event_carpools` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `driver_member_id` int(11) NOT NULL,
  `departure_location` varchar(255) NOT NULL,
  `departure_time` datetime NOT NULL,
  `available_seats` int(11) NOT NULL DEFAULT 1,
  `return_time` datetime DEFAULT NULL,
  `contact_info` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_driver_member_id` (`driver_member_id`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`driver_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Carpool Passengers
CREATE TABLE IF NOT EXISTS `carpool_passengers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `carpool_id` int(11) NOT NULL,
  `passenger_member_id` int(11) NOT NULL,
  `pickup_location` varchar(255) DEFAULT NULL,
  `special_instructions` text DEFAULT NULL,
  `status` enum('requested','confirmed','cancelled') NOT NULL DEFAULT 'requested',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_carpool_passenger` (`carpool_id`, `passenger_member_id`),
  KEY `idx_carpool_id` (`carpool_id`),
  KEY `idx_passenger_member_id` (`passenger_member_id`),
  FOREIGN KEY (`carpool_id`) REFERENCES `event_carpools` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`passenger_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- AUTOMATED REMINDER SEQUENCES
-- ============================================================================

-- Reminder Templates
CREATE TABLE IF NOT EXISTS `reminder_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `event_type` varchar(50) DEFAULT NULL,
  `days_before_event` int(11) NOT NULL,
  `reminder_type` enum('email','sms','both') NOT NULL DEFAULT 'email',
  `subject_template` varchar(255) NOT NULL,
  `message_template` text NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_days_before` (`days_before_event`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Scheduled Reminders
CREATE TABLE IF NOT EXISTS `scheduled_reminders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `reminder_type` enum('email','sms','both') NOT NULL,
  `scheduled_datetime` datetime NOT NULL,
  `sent_datetime` datetime DEFAULT NULL,
  `status` enum('scheduled','sent','failed','cancelled') NOT NULL DEFAULT 'scheduled',
  `failure_reason` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_scheduled_datetime` (`scheduled_datetime`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`template_id`) REFERENCES `reminder_templates` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- FAMILY MEMBER MANAGEMENT
-- ============================================================================

-- Family Relationships
CREATE TABLE IF NOT EXISTS `family_relationships` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `primary_member_id` int(11) NOT NULL COMMENT 'Main family member account',
  `related_member_id` int(11) NOT NULL COMMENT 'Family member being linked',
  `relationship_type` enum('spouse','child','parent','sibling','grandparent','grandchild','other') NOT NULL,
  `relationship_description` varchar(100) DEFAULT NULL,
  `can_manage_profile` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can edit related member profile',
  `can_rsvp_for` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can RSVP on behalf of related member',
  `is_emergency_contact` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `approved_by_related` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Approved by the related member',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_family_relationship` (`primary_member_id`, `related_member_id`),
  KEY `idx_primary_member` (`primary_member_id`),
  KEY `idx_related_member` (`related_member_id`),
  KEY `idx_relationship_type` (`relationship_type`),
  FOREIGN KEY (`primary_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`related_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `members` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Family Groups (for easier management)
CREATE TABLE IF NOT EXISTS `family_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `family_name` varchar(255) NOT NULL,
  `head_of_family_id` int(11) NOT NULL,
  `family_address` text DEFAULT NULL,
  `family_phone` varchar(20) DEFAULT NULL,
  `family_email` varchar(255) DEFAULT NULL,
  `anniversary_date` date DEFAULT NULL,
  `family_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_head_of_family` (`head_of_family_id`),
  FOREIGN KEY (`head_of_family_id`) REFERENCES `members` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Family Group Members
CREATE TABLE IF NOT EXISTS `family_group_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `family_group_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `role_in_family` varchar(100) DEFAULT NULL,
  `joined_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_family_member` (`family_group_id`, `member_id`),
  KEY `idx_family_group` (`family_group_id`),
  KEY `idx_member` (`member_id`),
  FOREIGN KEY (`family_group_id`) REFERENCES `family_groups` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- PRAYER REQUEST SYSTEM
-- ============================================================================

-- Prayer Requests
CREATE TABLE IF NOT EXISTS `prayer_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `category` enum('personal','family','health','work','ministry','community','other') NOT NULL DEFAULT 'personal',
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `privacy_level` enum('private','family','members','public') NOT NULL DEFAULT 'members',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
  `status` enum('active','answered','closed') NOT NULL DEFAULT 'active',
  `answered_description` text DEFAULT NULL,
  `answered_date` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `allow_comments` tinyint(1) NOT NULL DEFAULT 1,
  `notify_on_prayer` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_category` (`category`),
  KEY `idx_privacy_level` (`privacy_level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Prayer Responses (when someone prays for a request)
CREATE TABLE IF NOT EXISTS `prayer_responses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prayer_request_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `response_type` enum('prayed','praying','will_pray') NOT NULL DEFAULT 'prayed',
  `comment` text DEFAULT NULL,
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_prayer_response` (`prayer_request_id`, `member_id`),
  KEY `idx_prayer_request` (`prayer_request_id`),
  KEY `idx_member_id` (`member_id`),
  FOREIGN KEY (`prayer_request_id`) REFERENCES `prayer_requests` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Prayer Request Comments
CREATE TABLE IF NOT EXISTS `prayer_request_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prayer_request_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0,
  `is_approved` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_prayer_request` (`prayer_request_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`prayer_request_id`) REFERENCES `prayer_requests` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- SKILLS & VOLUNTEER MATCHING SYSTEM
-- ============================================================================

-- Skills Catalog
CREATE TABLE IF NOT EXISTS `skills_catalog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `skill_name` varchar(255) NOT NULL,
  `skill_category` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_skill_name` (`skill_name`),
  KEY `idx_skill_category` (`skill_category`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Member Skills
CREATE TABLE IF NOT EXISTS `member_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `skill_id` int(11) NOT NULL,
  `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
  `years_experience` int(11) DEFAULT NULL,
  `willing_to_teach` tinyint(1) NOT NULL DEFAULT 0,
  `willing_to_volunteer` tinyint(1) NOT NULL DEFAULT 1,
  `availability` text DEFAULT NULL COMMENT 'When available to use this skill',
  `notes` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL COMMENT 'Admin who verified this skill',
  `verified_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_member_skill` (`member_id`, `skill_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_skill_id` (`skill_id`),
  KEY `idx_proficiency` (`proficiency_level`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`skill_id`) REFERENCES `skills_catalog` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`verified_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Volunteer Opportunities
CREATE TABLE IF NOT EXISTS `volunteer_opportunities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `category` varchar(100) NOT NULL,
  `required_skills` text DEFAULT NULL COMMENT 'JSON array of required skill IDs',
  `preferred_skills` text DEFAULT NULL COMMENT 'JSON array of preferred skill IDs',
  `time_commitment` varchar(255) DEFAULT NULL,
  `schedule_type` enum('one_time','recurring','flexible','ongoing') NOT NULL DEFAULT 'one_time',
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `contact_person_id` int(11) NOT NULL,
  `max_volunteers` int(11) DEFAULT NULL,
  `min_age` int(11) DEFAULT NULL,
  `background_check_required` tinyint(1) NOT NULL DEFAULT 0,
  `training_required` tinyint(1) NOT NULL DEFAULT 0,
  `training_description` text DEFAULT NULL,
  `status` enum('draft','active','filled','completed','cancelled') NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_contact_person` (`contact_person_id`),
  FOREIGN KEY (`contact_person_id`) REFERENCES `members` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Volunteer Applications
CREATE TABLE IF NOT EXISTS `volunteer_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `opportunity_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `application_message` text DEFAULT NULL,
  `availability` text DEFAULT NULL,
  `relevant_experience` text DEFAULT NULL,
  `status` enum('pending','approved','rejected','withdrawn') NOT NULL DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `hours_committed` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_opportunity_member` (`opportunity_id`, `member_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Volunteer Hours Tracking
CREATE TABLE IF NOT EXISTS `volunteer_hours` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `opportunity_id` int(11) DEFAULT NULL,
  `date_served` date NOT NULL,
  `hours_served` decimal(5,2) NOT NULL,
  `description` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_date_served` (`date_served`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`verified_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- ENHANCED MEMBER PORTAL FEATURES
-- ============================================================================

-- Member Achievements/Milestones
CREATE TABLE IF NOT EXISTS `member_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `achievement_type` enum('volunteer_hours','event_attendance','anniversary','birthday','skill_milestone','other') NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `achievement_date` date NOT NULL,
  `points_awarded` int(11) DEFAULT 0,
  `badge_icon` varchar(255) DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_achievement_type` (`achievement_type`),
  KEY `idx_achievement_date` (`achievement_date`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Birthday and Anniversary Celebrations
CREATE TABLE IF NOT EXISTS `celebration_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `celebration_type` enum('birthday','anniversary','baptism','membership','other') NOT NULL,
  `celebration_date` date NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `allow_wishes` tinyint(1) NOT NULL DEFAULT 1,
  `notify_community` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_celebration_type` (`celebration_type`),
  KEY `idx_celebration_date` (`celebration_date`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Celebration Wishes/Messages
CREATE TABLE IF NOT EXISTS `celebration_wishes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `celebration_id` int(11) NOT NULL,
  `sender_member_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_celebration_id` (`celebration_id`),
  KEY `idx_sender_member_id` (`sender_member_id`),
  FOREIGN KEY (`celebration_id`) REFERENCES `celebration_events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`sender_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Member Profile Extensions
ALTER TABLE `members`
ADD COLUMN `profile_photo_path` varchar(255) DEFAULT NULL COMMENT 'High-resolution profile photo',
ADD COLUMN `cover_photo_path` varchar(255) DEFAULT NULL COMMENT 'Cover/banner photo for profile',
ADD COLUMN `bio` text DEFAULT NULL COMMENT 'Personal biography',
ADD COLUMN `interests` text DEFAULT NULL COMMENT 'JSON array of interests/hobbies',
ADD COLUMN `social_media_links` text DEFAULT NULL COMMENT 'JSON object of social media profiles',
ADD COLUMN `emergency_contact_name` varchar(255) DEFAULT NULL,
ADD COLUMN `emergency_contact_phone` varchar(20) DEFAULT NULL,
ADD COLUMN `emergency_contact_relationship` varchar(100) DEFAULT NULL,
ADD COLUMN `medical_conditions` text DEFAULT NULL COMMENT 'Important medical information',
ADD COLUMN `allergies` text DEFAULT NULL COMMENT 'Known allergies',
ADD COLUMN `preferred_communication` enum('email','sms','phone','mail') DEFAULT 'email',
ADD COLUMN `privacy_settings` text DEFAULT NULL COMMENT 'JSON object of privacy preferences',
ADD COLUMN `notification_settings` text DEFAULT NULL COMMENT 'JSON object of notification preferences',
ADD COLUMN `anniversary_date` date DEFAULT NULL COMMENT 'Wedding anniversary or membership anniversary',
ADD COLUMN `baptism_date` date DEFAULT NULL,
ADD COLUMN `membership_date` date DEFAULT NULL,
ADD COLUMN `volunteer_interests` text DEFAULT NULL COMMENT 'JSON array of volunteer interest categories',
ADD COLUMN `availability_schedule` text DEFAULT NULL COMMENT 'JSON object of general availability';

-- Member Activity Feed
CREATE TABLE IF NOT EXISTS `member_activity_feed` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `activity_type` enum('event_rsvp','prayer_request','volunteer_signup','achievement','birthday','anniversary','profile_update','other') NOT NULL,
  `activity_title` varchar(255) NOT NULL,
  `activity_description` text DEFAULT NULL,
  `related_entity_type` varchar(50) DEFAULT NULL COMMENT 'events, prayer_requests, etc.',
  `related_entity_id` int(11) DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_related_entity` (`related_entity_type`, `related_entity_id`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- INDEXES AND VIEWS FOR PERFORMANCE
-- ============================================================================

-- Comprehensive event view with all related data
CREATE OR REPLACE VIEW `events_with_details` AS
SELECT
    e.*,
    ec.name as category_name,
    ec.color_code as category_color,
    a.full_name as created_by_name,
    COUNT(DISTINCT er.id) as total_rsvps,
    COUNT(DISTINCT CASE WHEN er.response = 'yes' THEN er.id END) as yes_rsvps,
    COUNT(DISTINCT CASE WHEN er.response = 'no' THEN er.id END) as no_rsvps,
    COUNT(DISTINCT CASE WHEN er.response = 'maybe' THEN er.id END) as maybe_rsvps,
    COUNT(DISTINCT es.id) as session_count,
    COUNT(DISTINCT eg.id) as guest_count
FROM events e
LEFT JOIN event_categories ec ON e.category_id = ec.id
LEFT JOIN admins a ON e.created_by = a.id
LEFT JOIN event_rsvps er ON e.id = er.event_id
LEFT JOIN event_sessions es ON e.id = es.event_id
LEFT JOIN event_guests eg ON e.id = eg.event_id
GROUP BY e.id;

-- Member profile summary view
CREATE OR REPLACE VIEW `member_profiles_summary` AS
SELECT
    m.*,
    COUNT(DISTINCT er.id) as total_event_rsvps,
    COUNT(DISTINCT va.id) as volunteer_applications,
    COUNT(DISTINCT vh.id) as volunteer_hours_logged,
    COUNT(DISTINCT pr.id) as prayer_requests_count,
    COUNT(DISTINCT ms.id) as skills_count,
    COUNT(DISTINCT fr.id) as family_relationships_count
FROM members m
LEFT JOIN event_rsvps er ON m.id = er.member_id
LEFT JOIN volunteer_applications va ON m.id = va.member_id
LEFT JOIN volunteer_hours vh ON m.id = vh.member_id
LEFT JOIN prayer_requests pr ON m.id = pr.member_id
LEFT JOIN member_skills ms ON m.id = ms.member_id
LEFT JOIN family_relationships fr ON m.id = fr.primary_member_id OR m.id = fr.related_member_id
GROUP BY m.id;

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert default skill categories
INSERT IGNORE INTO `skills_catalog` (`skill_name`, `skill_category`, `description`, `created_by`) VALUES
('Public Speaking', 'Communication', 'Ability to speak confidently in front of groups', 1),
('Music - Vocals', 'Music & Arts', 'Singing and vocal performance', 1),
('Music - Piano', 'Music & Arts', 'Piano playing skills', 1),
('Music - Guitar', 'Music & Arts', 'Guitar playing skills', 1),
('Teaching', 'Education', 'Ability to teach and educate others', 1),
('Childcare', 'Care & Support', 'Experience caring for children', 1),
('Counseling', 'Care & Support', 'Providing emotional and spiritual support', 1),
('Event Planning', 'Organization', 'Planning and coordinating events', 1),
('Photography', 'Media & Technology', 'Photography skills for events and documentation', 1),
('Video Production', 'Media & Technology', 'Video recording and editing', 1),
('Web Development', 'Technology', 'Website development and maintenance', 1),
('Graphic Design', 'Media & Technology', 'Creating visual designs and materials', 1),
('Cooking', 'Hospitality', 'Preparing meals for groups', 1),
('Hospitality', 'Hospitality', 'Welcoming and hosting guests', 1),
('Administrative', 'Organization', 'Office and administrative tasks', 1),
('Financial Management', 'Organization', 'Managing finances and budgets', 1),
('Construction', 'Maintenance & Repair', 'Building and construction skills', 1),
('Electrical Work', 'Maintenance & Repair', 'Electrical repairs and installation', 1),
('Plumbing', 'Maintenance & Repair', 'Plumbing repairs and maintenance', 1),
('Gardening', 'Maintenance & Repair', 'Landscaping and garden maintenance', 1);

-- Insert default reminder templates
INSERT IGNORE INTO `reminder_templates` (`name`, `description`, `event_type`, `days_before_event`, `reminder_type`, `subject_template`, `message_template`, `created_by`) VALUES
('Event Reminder - 7 Days', 'General event reminder sent 7 days before', NULL, 7, 'email', 'Reminder: {{event_title}} is coming up!', 'Hi {{member_name}},\n\nThis is a friendly reminder that {{event_title}} is scheduled for {{event_date}} at {{event_location}}.\n\nWe look forward to seeing you there!\n\nBlessings,\n{{organization_name}}', 1),
('Event Reminder - 1 Day', 'Final reminder sent 1 day before event', NULL, 1, 'both', 'Tomorrow: {{event_title}}', 'Hi {{member_name}},\n\nJust a quick reminder that {{event_title}} is tomorrow ({{event_date}}) at {{event_location}}.\n\nSee you there!\n\n{{organization_name}}', 1),
('Workshop Reminder - 3 Days', 'Workshop-specific reminder', 'workshop', 3, 'email', 'Workshop Reminder: {{event_title}}', 'Hi {{member_name}},\n\n{{event_title}} is coming up in 3 days!\n\nDate: {{event_date}}\nLocation: {{event_location}}\n\nPlease bring: {{materials_needed}}\n\nLooking forward to learning together!\n\n{{organization_name}}', 1);

-- ============================================================================
-- SCHEMA ENHANCEMENT COMPLETE
-- ============================================================================
