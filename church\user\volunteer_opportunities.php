<?php
session_start();
require_once '../config.php';

// Check if user is authenticated
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['apply_for_opportunity'])) {
            // Check if user already applied
            $stmt = $pdo->prepare("
                SELECT id FROM volunteer_applications 
                WHERE opportunity_id = ? AND member_id = ?
            ");
            $stmt->execute([$_POST['opportunity_id'], $userId]);
            
            if ($stmt->fetch()) {
                $error = "You have already applied for this opportunity.";
            } else {
                // Submit application
                $stmt = $pdo->prepare("
                    INSERT INTO volunteer_applications 
                    (opportunity_id, member_id, application_message, availability, relevant_experience)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $_POST['opportunity_id'],
                    $userId,
                    $_POST['application_message'],
                    $_POST['availability'],
                    $_POST['relevant_experience']
                ]);
                
                $message = "Your application has been submitted successfully!";
            }
        }
        
        if (isset($_POST['withdraw_application'])) {
            // Withdraw application
            $stmt = $pdo->prepare("
                UPDATE volunteer_applications 
                SET status = 'withdrawn' 
                WHERE id = ? AND member_id = ?
            ");
            
            $stmt->execute([$_POST['application_id'], $userId]);
            $message = "Your application has been withdrawn.";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get available volunteer opportunities
try {
    $stmt = $pdo->prepare("
        SELECT vo.*, m.full_name as contact_name, m.email as contact_email,
               COUNT(DISTINCT va.id) as application_count,
               COUNT(DISTINCT CASE WHEN va.status = 'approved' THEN va.id END) as filled_positions,
               MAX(CASE WHEN va.member_id = ? THEN va.status END) as my_application_status,
               MAX(CASE WHEN va.member_id = ? THEN va.id END) as my_application_id
        FROM volunteer_opportunities vo
        LEFT JOIN members m ON vo.contact_person_id = m.id
        LEFT JOIN volunteer_applications va ON vo.id = va.opportunity_id
        WHERE vo.status = 'active'
        GROUP BY vo.id
        ORDER BY vo.created_at DESC
    ");
    $stmt->execute([$userId, $userId]);
    $opportunities = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $opportunities = [];
    $error = "Error loading opportunities: " . $e->getMessage();
}

// Get user's applications
try {
    $stmt = $pdo->prepare("
        SELECT va.*, vo.title as opportunity_title, vo.description as opportunity_description,
               m.full_name as contact_name, m.email as contact_email
        FROM volunteer_applications va
        JOIN volunteer_opportunities vo ON va.opportunity_id = vo.id
        LEFT JOIN members m ON vo.contact_person_id = m.id
        WHERE va.member_id = ? AND va.status != 'withdrawn'
        ORDER BY va.created_at DESC
    ");
    $stmt->execute([$userId]);
    $my_applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $my_applications = [];
}

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$userData) {
        header('Location: login.php');
        exit;
    }
} catch (PDOException $e) {
    $error = "Error loading user data: " . $e->getMessage();
    $userData = ['first_name' => 'User', 'full_name' => 'User'];
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volunteer Opportunities - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <?php include 'includes/theme_css.php'; ?>
    
    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .volunteer-container {
            margin-top: 2rem;
        }
        
        .volunteer-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .volunteer-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .opportunity-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .opportunity-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .category-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .skill-tag {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto"></div>
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="prayer_requests.php">
                            <i class="bi bi-heart"></i> Prayer Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="volunteer_opportunities.php">
                            <i class="bi bi-person-workspace"></i> Volunteer
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="family_management.php"><i class="bi bi-people"></i> Family Management</a></li>
                            <li><a class="dropdown-item" href="prayer_requests.php"><i class="bi bi-heart"></i> Prayer Requests</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container volunteer-container">
        <!-- Volunteer Header -->
        <div class="volunteer-header">
            <h1><i class="bi bi-person-workspace"></i> Volunteer Opportunities</h1>
            <p class="mb-0">Find meaningful ways to serve and make a difference in your community</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- My Applications Section -->
        <?php if (!empty($my_applications)): ?>
        <div class="volunteer-card">
            <h5><i class="bi bi-file-earmark-person"></i> My Applications</h5>
            <div class="row">
                <?php foreach ($my_applications as $app): ?>
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($app['opportunity_title']); ?></h6>
                                <span class="badge bg-<?php
                                    echo $app['status'] === 'approved' ? 'success' :
                                        ($app['status'] === 'rejected' ? 'danger' : 'warning');
                                ?>">
                                    <?php echo ucfirst($app['status']); ?>
                                </span>
                            </div>
                            <p class="card-text small text-muted">
                                Applied: <?php echo date('M j, Y', strtotime($app['created_at'])); ?>
                            </p>
                            <?php if ($app['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="withdrawApplication(<?php echo $app['id']; ?>, '<?php echo htmlspecialchars($app['opportunity_title']); ?>')">
                                    <i class="bi bi-x-circle"></i> Withdraw
                                </button>
                            <?php endif; ?>
                            <?php if ($app['review_notes']): ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>Review Notes:</strong> <?php echo htmlspecialchars($app['review_notes']); ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Available Opportunities -->
        <div class="volunteer-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5><i class="bi bi-briefcase"></i> Available Opportunities</h5>
                <div>
                    <span class="badge bg-primary"><?php echo count($opportunities); ?> Total</span>
                </div>
            </div>

            <?php if (empty($opportunities)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">No Volunteer Opportunities Available</h4>
                    <p class="text-muted">Check back later for new volunteer opportunities.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($opportunities as $opp): ?>
                    <div class="col-lg-6 mb-4">
                        <div class="opportunity-card h-100">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1"><?php echo htmlspecialchars($opp['title']); ?></h5>
                                    <span class="badge bg-secondary category-badge"><?php echo ucfirst($opp['category']); ?></span>
                                </div>
                                <div class="text-end">
                                    <?php if ($opp['my_application_status']): ?>
                                        <span class="badge bg-<?php
                                            echo $opp['my_application_status'] === 'approved' ? 'success' :
                                                ($opp['my_application_status'] === 'rejected' ? 'danger' : 'warning');
                                        ?> status-badge">
                                            <?php echo ucfirst($opp['my_application_status']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <p class="text-muted mb-3"><?php echo htmlspecialchars($opp['description']); ?></p>

                            <div class="mb-3">
                                <div class="row text-sm">
                                    <div class="col-6">
                                        <strong>Contact:</strong><br>
                                        <small><?php echo htmlspecialchars($opp['contact_name']); ?></small>
                                    </div>
                                    <div class="col-6">
                                        <strong>Schedule:</strong><br>
                                        <small><?php echo ucfirst(str_replace('_', ' ', $opp['schedule_type'])); ?></small>
                                    </div>
                                </div>
                            </div>

                            <?php if ($opp['time_commitment']): ?>
                            <div class="mb-2">
                                <strong>Time Commitment:</strong>
                                <small><?php echo htmlspecialchars($opp['time_commitment']); ?></small>
                            </div>
                            <?php endif; ?>

                            <?php if ($opp['location']): ?>
                            <div class="mb-2">
                                <strong>Location:</strong>
                                <small><?php echo htmlspecialchars($opp['location']); ?></small>
                            </div>
                            <?php endif; ?>

                            <?php if ($opp['required_skills']): ?>
                            <div class="mb-2">
                                <strong>Required Skills:</strong>
                                <div class="skills-list">
                                    <?php
                                    $skills = json_decode($opp['required_skills'], true);
                                    if ($skills) {
                                        foreach ($skills as $skill) {
                                            echo '<span class="skill-tag">' . htmlspecialchars(trim($skill)) . '</span>';
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="mt-3 d-flex justify-content-between align-items-center">
                                <div>
                                    <?php if ($opp['max_volunteers']): ?>
                                        <small class="text-muted">
                                            <?php echo $opp['filled_positions']; ?>/<?php echo $opp['max_volunteers']; ?> filled
                                        </small>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if (!$opp['my_application_status']): ?>
                                        <?php if ($opp['max_volunteers'] && $opp['filled_positions'] >= $opp['max_volunteers']): ?>
                                            <span class="badge bg-secondary">Position Filled</span>
                                        <?php else: ?>
                                            <button class="btn btn-primary btn-sm"
                                                    onclick="applyForOpportunity(<?php echo $opp['id']; ?>, '<?php echo htmlspecialchars($opp['title']); ?>')">
                                                <i class="bi bi-hand-thumbs-up"></i> Apply Now
                                            </button>
                                        <?php endif; ?>
                                    <?php elseif ($opp['my_application_status'] === 'pending'): ?>
                                        <button class="btn btn-sm btn-outline-danger"
                                                onclick="withdrawApplication(<?php echo $opp['my_application_id']; ?>, '<?php echo htmlspecialchars($opp['title']); ?>')">
                                            <i class="bi bi-x-circle"></i> Withdraw
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Apply for Opportunity Modal -->
    <div class="modal fade" id="applyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Apply for Volunteer Opportunity</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="opportunity_id" id="apply_opportunity_id">

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            You are applying for: <strong id="apply_opportunity_title"></strong>
                        </div>

                        <div class="mb-3">
                            <label for="application_message" class="form-label">Why are you interested in this opportunity? *</label>
                            <textarea class="form-control" name="application_message" rows="4" required
                                      placeholder="Tell us why you're interested and what you hope to contribute..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="availability" class="form-label">Your Availability</label>
                            <textarea class="form-control" name="availability" rows="3"
                                      placeholder="When are you available? (e.g., weekends, evenings, specific days)"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="relevant_experience" class="form-label">Relevant Experience</label>
                            <textarea class="form-control" name="relevant_experience" rows="3"
                                      placeholder="Describe any relevant experience, skills, or qualifications..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="apply_for_opportunity" class="btn btn-primary">
                            <i class="bi bi-send"></i> Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function applyForOpportunity(opportunityId, title) {
            document.getElementById('apply_opportunity_id').value = opportunityId;
            document.getElementById('apply_opportunity_title').textContent = title;

            const modal = new bootstrap.Modal(document.getElementById('applyModal'));
            modal.show();
        }

        function withdrawApplication(applicationId, title) {
            if (confirm(`Are you sure you want to withdraw your application for "${title}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="application_id" value="${applicationId}">
                    <input type="hidden" name="withdraw_application" value="1">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
