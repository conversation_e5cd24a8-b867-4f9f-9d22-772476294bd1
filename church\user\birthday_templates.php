<?php
/**
 * Birthday Templates Page - Redesigned
 * 
 * Displays available birthday templates in a clean 2x2 card layout
 */

// Include configuration first
require_once '../config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Get site settings
$sitename = get_site_setting('site_name', 'Church Management System');

// Get birthday templates from database
$stmt = $pdo->prepare("
    SELECT
        id,
        template_name as name,
        subject,
        content as template_content,
        template_category as category,
        COALESCE(
            (SELECT COUNT(*) FROM email_template_usage
             WHERE template_id = email_templates.id AND template_type = 'birthday'),
            0
        ) as usage_count
    FROM email_templates
    WHERE is_birthday_template = 1
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll();

// Get today's birthdays for quick access
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, image_path
    FROM members 
    WHERE status = 'active' 
    AND id != ? 
    AND MONTH(birth_date) = MONTH(CURDATE()) 
    AND DAY(birth_date) = DAY(CURDATE())
    ORDER BY first_name
");
$stmt->execute([$userId]);
$todaysBirthdays = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Templates - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
        }

        .navbar-brand {
            font-weight: 700;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        .navbar-nav .nav-link:hover, .navbar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }

        .template-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .template-preview {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            max-height: 120px; /* Reduced height for better grid layout */
            overflow: hidden;
            position: relative;
            font-size: 0.8rem; /* Smaller font for better fit */
            line-height: 1.3;
            flex-grow: 1;
        }

        .template-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, #f8f9fa);
        }

        .template-actions {
            margin-top: auto;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }

        .birthday-quick-access {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* 3 columns for 3x3 layout */
            gap: 1.5rem;
            max-width: 1200px; /* Wider for better 3x3 layout */
            margin: 0 auto;
        }

        @media (max-width: 992px) {
            .templates-grid {
                grid-template-columns: repeat(2, 1fr); /* 2 columns on tablets */
                max-width: 800px;
            }
        }

        @media (max-width: 768px) {
            .templates-grid {
                grid-template-columns: 1fr; /* Single column on mobile */
                max-width: 400px;
            }
        }


    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <?php
                $headerLogo = get_site_setting('header_logo', '');
                $mainLogo = get_site_setting('main_logo', '');
                $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;
                if (!empty($logoToUse)): ?>
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_organization_name(); ?>"
                         class="navbar-logo me-2">
                    <?php echo htmlspecialchars(get_organization_name()); ?>
                <?php else: ?>
                    <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
                <?php endif; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto"></div>
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="prayer_requests.php">
                            <i class="bi bi-heart"></i> Prayer Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="family_management.php"><i class="bi bi-people"></i> Family Management</a></li>
                            <li><a class="dropdown-item" href="prayer_requests.php"><i class="bi bi-heart"></i> Prayer Requests</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="page-header">
            <h2><i class="bi bi-gift"></i> Birthday Templates</h2>
            <p class="text-muted mb-0">Choose from beautiful templates to send personalized birthday messages to your church family.</p>
        </div>



        <!-- Today's Birthdays Quick Access -->
        <?php if (!empty($todaysBirthdays)): ?>
        <div class="birthday-quick-access">
            <h5><i class="bi bi-calendar-heart"></i> Send Birthday Wishes Today</h5>
            <p class="mb-3">These members are celebrating their birthday today!</p>
            <div class="d-flex flex-wrap gap-2">
                <?php foreach ($todaysBirthdays as $birthday): ?>
                <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>" 
                   class="btn btn-light btn-sm">
                    <i class="bi bi-envelope-heart"></i> <?php echo htmlspecialchars($birthday['first_name']); ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Birthday Templates -->
        <?php if (empty($templates)): ?>
        <div class="text-center py-5">
            <i class="bi bi-gift display-1 text-muted"></i>
            <h4 class="mt-3">No Templates Available</h4>
            <p class="text-muted">Birthday templates will appear here once they are created by administrators.</p>
        </div>
        <?php else: ?>
        <div class="templates-grid">
            <?php foreach ($templates as $template): ?>
            <div class="template-card">
                <div class="template-preview">
                    <?php
                    // Create a sample preview of the template
                    $previewContent = $template['template_content'];

                    // Replace placeholders with sample data
                    $replacements = [
                        '{full_name}' => 'John Doe',
                        '{first_name}' => 'John',
                        '{last_name}' => 'Doe',
                        '{organization_name}' => $sitename,
                        '{sender_name}' => $userData['first_name'],
                        '{{recipient_name}}' => 'John Doe',
                        '{{sender_name}}' => $userData['first_name'],
                        '{{church_name}}' => $sitename,
                        '[name]' => 'John Doe',
                        '[sender]' => $userData['first_name'],
                        '[church]' => $sitename
                    ];

                    foreach ($replacements as $placeholder => $value) {
                        $previewContent = str_replace($placeholder, $value, $previewContent);
                    }

                    // Strip HTML tags for cleaner preview
                    $previewContent = strip_tags($previewContent);

                    // Limit preview content length for better grid layout
                    if (strlen($previewContent) > 200) {
                        $previewContent = substr($previewContent, 0, 200) . '...';
                    }

                    echo htmlspecialchars($previewContent);
                    ?>
                </div>
                
                <h6 class="mb-2"><?php echo htmlspecialchars($template['name']); ?></h6>
                
                <?php if ($template['subject']): ?>
                <p class="text-muted small mb-3"><?php echo htmlspecialchars($template['subject']); ?></p>
                <?php endif; ?>
                
                <div class="template-actions">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">
                            <i class="bi bi-heart"></i> Used <?php echo $template['usage_count']; ?> times
                        </small>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm"
                                onclick="previewTemplate(<?php echo $template['id']; ?>)">
                            <i class="bi bi-eye"></i> Preview Template
                        </button>
                        <a href="send_birthday_message.php?template_id=<?php echo $template['id']; ?>"
                           class="btn btn-primary btn-sm">
                            <i class="bi bi-envelope"></i> Use This Template
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Template Preview Modal -->
    <div class="modal fade" id="templatePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Template Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="templatePreviewContent">
                    <!-- Preview content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="useTemplateBtn">Use This Template</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewTemplate(templateId) {
            // Load template preview via AJAX
            fetch('ajax/preview_template.php?id=' + templateId)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('templatePreviewContent').innerHTML = html;
                    document.getElementById('useTemplateBtn').onclick = function() {
                        window.location.href = 'send_birthday_message.php?template_id=' + templateId;
                    };
                    new bootstrap.Modal(document.getElementById('templatePreviewModal')).show();
                })
                .catch(error => {
                    console.error('Error loading template preview:', error);
                    alert('Error loading template preview. Please try again.');
                });
        }
    </script>
</body>
</html>
