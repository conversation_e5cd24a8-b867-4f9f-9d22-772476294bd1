<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config.php';

// Page title and header info
$page_title = 'Birthday Management';
$page_header = 'Birthday Management';
$page_description = 'Manage member birthdays and celebrations';

// Get current month and year
$currentMonth = date('n');
$currentYear = date('Y');

// Handle month/year selection
if (isset($_GET['month']) && isset($_GET['year'])) {
    $selectedMonth = (int)$_GET['month'];
    $selectedYear = (int)$_GET['year'];
} else {
    $selectedMonth = $currentMonth;
    $selectedYear = $currentYear;
}

// Get birthdays for selected month
try {
    $stmt = $pdo->prepare("
        SELECT id, first_name, last_name, full_name, email, birth_date, 
               DAY(birth_date) as birth_day,
               YEAR(CURDATE()) - YEAR(birth_date) - (DATE_FORMAT(CURDATE(), '%m-%d') < DATE_FORMAT(birth_date, '%m-%d')) as age
        FROM members 
        WHERE MONTH(birth_date) = ? AND status = 'active'
        ORDER BY DAY(birth_date)
    ");
    $stmt->execute([$selectedMonth]);
    $birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $birthdays = [];
    $error = "Error loading birthdays: " . $e->getMessage();
}

// Get birthday statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_birthdays,
            COUNT(CASE WHEN MONTH(birth_date) = MONTH(CURDATE()) THEN 1 END) as this_month,
            COUNT(CASE WHEN DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(CURDATE(), '%m-%d') THEN 1 END) as today
        FROM members 
        WHERE birth_date IS NOT NULL AND status = 'active'
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = ['total_birthdays' => 0, 'this_month' => 0, 'today' => 0];
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="col-md-9 col-lg-10 main-content">
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-gift fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['today']; ?></h3>
                                    <small>Birthdays Today</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-calendar-month fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['this_month']; ?></h3>
                                    <small>This Month</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['total_birthdays']; ?></h3>
                                    <small>Total Members</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Month/Year Selection -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-calendar3"></i> Select Month & Year</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="month" class="form-label">Month</label>
                            <select name="month" id="month" class="form-select">
                                <?php
                                $months = [
                                    1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                                    5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                                    9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                                ];
                                foreach ($months as $num => $name) {
                                    $selected = ($num == $selectedMonth) ? 'selected' : '';
                                    echo "<option value='$num' $selected>$name</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="year" class="form-label">Year</label>
                            <select name="year" id="year" class="form-select">
                                <?php
                                for ($year = $currentYear - 5; $year <= $currentYear + 5; $year++) {
                                    $selected = ($year == $selectedYear) ? 'selected' : '';
                                    echo "<option value='$year' $selected>$year</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">View Birthdays</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Birthdays List -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-cake2"></i> Birthdays in <?php echo $months[$selectedMonth] . ' ' . $selectedYear; ?></h5>
                </div>
                <div class="card-body">
                    <?php if (empty($birthdays)): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> No birthdays found for <?php echo $months[$selectedMonth] . ' ' . $selectedYear; ?>.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Age</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($birthdays as $birthday): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $months[$selectedMonth] . ' ' . $birthday['birth_day']; ?></strong>
                                                <?php if ($birthday['birth_day'] == date('j') && $selectedMonth == $currentMonth): ?>
                                                    <span class="badge bg-warning ms-2">Today!</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($birthday['full_name'] ?: $birthday['first_name'] . ' ' . $birthday['last_name']); ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($birthday['email']); ?></td>
                                            <td>
                                                <?php if ($selectedYear == $currentYear): ?>
                                                    <?php echo $birthday['age']; ?> years
                                                <?php else: ?>
                                                    <?php echo $selectedYear - date('Y', strtotime($birthday['birth_date'])); ?> years
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="members.php?action=edit&id=<?php echo $birthday['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-person"></i> View
                                                </a>
                                                <button class="btn btn-sm btn-outline-success" 
                                                        onclick="sendBirthdayWish(<?php echo $birthday['id']; ?>)">
                                                    <i class="bi bi-envelope"></i> Send Wish
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
function sendBirthdayWish(memberId) {
    if (confirm('Send birthday wishes to this member?')) {
        // This would integrate with your email system
        alert('Birthday wish functionality would be implemented here with your email system.');
    }
}
</script>

<?php include 'includes/footer.php'; ?>
