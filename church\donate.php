<?php
require_once 'config.php';

// Function to check if a table exists
function tableExists($conn, $tableName) {
    try {
        // Use a more reliable method to check if table exists
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        return in_array($tableName, $tables);
    } catch (PDOException $e) {
        error_log("Error checking if table exists: " . $e->getMessage());
        return false;
    }
}

// Function to update a payment setting
function updatePaymentSetting($conn, $key, $value) {
    try {
        $stmt = $conn->prepare("INSERT INTO payment_settings (setting_key, setting_value) 
                               VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$key, $value]);
        return true;
    } catch (PDOException $e) {
        error_log("Error updating payment setting: " . $e->getMessage());
        return false;
    }
}

// Check if payment_settings table exists and create it if it doesn't
if (!tableExists($pdo, 'payment_settings')) {
    try {
        // Create payment_settings table
        $sql = "CREATE TABLE IF NOT EXISTS payment_settings (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        $pdo->exec($sql);
        
        // Insert default settings
        $defaultSettings = [
            'paypal_enabled' => '1',
            'paypal_client_id' => '',
            'paypal_client_secret' => '',
            'paypal_sandbox_mode' => '1',
            'stripe_enabled' => '0',
            'stripe_publishable_key' => '',
            'stripe_secret_key' => '',
            'stripe_webhook_secret' => '',
            'stripe_test_mode' => '1',
            'donations_enabled' => '1',
            'birthday_gifts_enabled' => '1',
            'minimum_donation_amount' => '5',
            'default_currency' => 'USD',
            'donation_success_message' => 'Thank you for your generous donation!',
            'donation_email_template' => 'Thank you for your donation of {amount} {currency}. Your support helps us continue our mission.',
            'payment_notification_email' => ''
        ];
        
        foreach ($defaultSettings as $key => $value) {
            updatePaymentSetting($pdo, $key, $value);
        }
    } catch (PDOException $e) {
        error_log("Error creating payment_settings table: " . $e->getMessage());
        // Continue with default settings even if table creation fails
    }
}

// Get payment settings
try {
    $stmt = $pdo->prepare("SELECT * FROM payment_settings");
    $stmt->execute();
    $payment_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $payment_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("Error loading payment settings: " . $e->getMessage());
    $payment_settings = [
        'paypal_enabled' => '1',
        'stripe_enabled' => '0',
        'donations_enabled' => '1',
        'birthday_gifts_enabled' => '1',
        'minimum_donation_amount' => '5',
        'default_currency' => 'USD'
    ];
}

// Check if donations are enabled
$donations_enabled = isset($payment_settings['donations_enabled']) && $payment_settings['donations_enabled'] === '1';
$birthday_gifts_enabled = isset($payment_settings['birthday_gifts_enabled']) && $payment_settings['birthday_gifts_enabled'] === '1';
$minimum_donation = floatval($payment_settings['minimum_donation_amount'] ?? 5);
$default_currency = $payment_settings['default_currency'] ?? 'USD';

// Get list of members with upcoming birthdays (within next 30 days) if birthday gifts are enabled
$birthday_members = [];
if ($birthday_gifts_enabled) {
    try {
        $sql = "SELECT id, full_name, birth_date 
                FROM members 
                WHERE birth_date IS NOT NULL 
                AND (
                    (MONTH(birth_date) = MONTH(CURRENT_DATE) AND DAY(birth_date) >= DAY(CURRENT_DATE))
                    OR 
                    (MONTH(birth_date) = MONTH(DATE_ADD(CURRENT_DATE, INTERVAL 1 MONTH)) 
                     AND DAY(birth_date) <= DAY(DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)))
                )
                ORDER BY MONTH(birth_date), DAY(birth_date)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $birthday_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching birthday members: " . $e->getMessage());
    }
}

// Currency options
$currencies = [
    'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
    'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
    'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦'],
    'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh'],
    'UGX' => ['name' => 'Ugandan Shilling', 'symbol' => 'USh'],
    'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => 'GH₵']
];

// Get currency symbol
$currency_symbol = $currencies[$default_currency]['symbol'] ?? '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Make a Donation - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">

    <?php include 'includes/frontend_css.php'; ?>
    <?php if (isset($payment_settings['stripe_enabled']) && $payment_settings['stripe_enabled'] === '1'): ?>
    <script src="https://js.stripe.com/v3/"></script>
    <?php endif; ?>
    <style>
        body {
            padding: 0;
            margin: 0;
            background: transparent;
        }
        .container {
            padding: 20px;
        }
        .donation-option {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .donation-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .donation-option.selected {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        #custom-amount {
            font-size: 2rem;
            height: auto;
            padding: 0.5rem 1rem;
        }
        .currency-symbol {
            font-size: 2rem;
            line-height: 1.5;
        }
        .payment-method-option {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .payment-method-option.selected {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h2 class="mb-3">Support Our Ministry</h2>
            <p class="lead">Your generous donations help us continue our mission and serve our community.</p>
        </div>
        
        <?php if (!$donations_enabled): ?>
            <div class="alert alert-warning">
                <h4>Donations are currently disabled</h4>
                <p>The donation system is currently disabled. Please contact the administrator for more information.</p>
            </div>
        <?php else: ?>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <form id="donation-form" action="process_donation.php" method="POST">
                        <!-- Donation Type -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Select Donation Type</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="donation_type" id="general_donation" value="general" checked>
                                    <label class="form-check-label" for="general_donation">
                                        General Donation
                                    </label>
                                </div>
                                
                                <?php if ($birthday_gifts_enabled && !empty($birthday_members)): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="donation_type" id="birthday_gift" value="birthday_gift">
                                    <label class="form-check-label" for="birthday_gift">
                                        Birthday Gift
                                    </label>
                                </div>
                                
                                <div id="birthday-member-select" class="mt-3" style="display: none;">
                                    <select class="form-select" name="recipient_id" disabled>
                                        <option value="">Select a member</option>
                                        <?php foreach ($birthday_members as $member): ?>
                                            <?php 
                                            $birth_date = new DateTime($member['birth_date']);
                                            $formatted_date = $birth_date->format('M d');
                                            ?>
                                            <option value="<?php echo $member['id']; ?>">
                                                <?php echo htmlspecialchars($member['full_name'] . ' (' . $formatted_date . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Donation Amount -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Select Amount</h5>
                                <div class="row g-3 mb-4">
                                    <div class="col-6 col-md-3">
                                        <div class="donation-option card text-center p-3" data-amount="10">
                                            <h4><?php echo $currency_symbol; ?>10</h4>
                                        </div>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <div class="donation-option card text-center p-3" data-amount="25">
                                            <h4><?php echo $currency_symbol; ?>25</h4>
                                        </div>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <div class="donation-option card text-center p-3" data-amount="50">
                                            <h4><?php echo $currency_symbol; ?>50</h4>
                                        </div>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <div class="donation-option card text-center p-3" data-amount="100">
                                            <h4><?php echo $currency_symbol; ?>100</h4>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="custom-amount" class="form-label">Or enter custom amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text currency-symbol"><?php echo $currency_symbol; ?></span>
                                        <input type="number" class="form-control" id="custom-amount" name="amount" min="<?php echo $minimum_donation; ?>" step="0.01" placeholder="0.00" required>
                                    </div>
                                    <div class="form-text">Minimum donation: <?php echo $currency_symbol . $minimum_donation; ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="currency" class="form-label">Currency</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <?php foreach ($currencies as $code => $details): ?>
                                            <option value="<?php echo $code; ?>" <?php echo $code === $default_currency ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($code . ' - ' . $details['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Donor Information -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Your Information</h5>
                                <div class="mb-3">
                                    <label for="donor_name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="donor_name" name="donor_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="donor_email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="donor_email" name="donor_email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message (Optional)</label>
                                    <textarea class="form-control" id="message" name="message" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Method -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Payment Method</h5>
                                <div class="row g-3">
                                    <?php 
                                    // Check if any payment methods are enabled
                                    $paypal_enabled = isset($payment_settings['paypal_enabled']) && $payment_settings['paypal_enabled'] === '1';
                                    $stripe_enabled = isset($payment_settings['stripe_enabled']) && $payment_settings['stripe_enabled'] === '1';
                                    
                                    // If no payment methods are enabled, default to PayPal
                                    if (!$paypal_enabled && !$stripe_enabled) {
                                        $paypal_enabled = true;
                                    }
                                    
                                    if ($paypal_enabled): 
                                    ?>
                                    <div class="col-md-6">
                                        <div class="payment-method-option card h-100">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal" checked>
                                                    <label class="form-check-label" for="paypal">
                                                        <img src="assets/images/paypal-logo.png" alt="PayPal" height="30" onerror="this.onerror=null; this.src='assets/images/default-logo.png'; this.alt='PayPal';">
                                                        PayPal
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($stripe_enabled): ?>
                                    <div class="col-md-6">
                                        <div class="payment-method-option card h-100">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment_method" id="stripe" value="stripe" <?php echo !$paypal_enabled ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="stripe">
                                                        <img src="assets/images/stripe-logo.png" alt="Stripe" height="30" onerror="this.onerror=null; this.src='assets/images/default-logo.png'; this.alt='Stripe';">
                                                        Stripe
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Stripe Elements Placeholder -->
                                <div id="stripe-elements" class="mt-4" style="display: none;">
                                    <div id="card-element" class="form-control mb-3">
                                        <!-- Stripe Elements will be inserted here -->
                                    </div>
                                    <div id="card-errors" class="invalid-feedback" role="alert"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">Donate Now</button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle donation type selection
            const donationType = document.querySelectorAll('input[name="donation_type"]');
            const birthdaySelect = document.getElementById('birthday-member-select');
            
            donationType.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (birthdaySelect) {
                        const recipientSelect = birthdaySelect.querySelector('select');
                        if (this.value === 'birthday_gift') {
                            birthdaySelect.style.display = 'block';
                            recipientSelect.disabled = false;
                            recipientSelect.required = true;
                        } else {
                            birthdaySelect.style.display = 'none';
                            recipientSelect.disabled = true;
                            recipientSelect.required = false;
                        }
                    }
                });
            });
            
            // Handle donation amount selection
            const donationOptions = document.querySelectorAll('.donation-option');
            const customAmount = document.getElementById('custom-amount');
            
            donationOptions.forEach(option => {
                option.addEventListener('click', function() {
                    donationOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    customAmount.value = this.dataset.amount;
                });
            });
            
            // Handle payment method selection
            const paymentMethods = document.querySelectorAll('.payment-method-option');
            const stripeElements = document.getElementById('stripe-elements');
            
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                    paymentMethods.forEach(m => m.classList.remove('selected'));
                    this.classList.add('selected');
                    
                    if (stripeElements) {
                        stripeElements.style.display = radio.value === 'stripe' ? 'block' : 'none';
                    }
                });
            });
            
            <?php if (isset($payment_settings['stripe_enabled']) && $payment_settings['stripe_enabled'] === '1'): ?>
            // Initialize Stripe
            const stripe = Stripe('<?php echo $payment_settings['stripe_publishable_key']; ?>');
            const elements = stripe.elements();
            const card = elements.create('card');
            card.mount('#card-element');
            
            card.addEventListener('change', function(event) {
                const displayError = document.getElementById('card-errors');
                if (event.error) {
                    displayError.textContent = event.error.message;
                    displayError.style.display = 'block';
                } else {
                    displayError.textContent = '';
                    displayError.style.display = 'none';
                }
            });
            
            // Handle form submission
            const form = document.getElementById('donation-form');
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
                
                if (paymentMethod === 'stripe') {
                    stripe.createToken(card).then(function(result) {
                        if (result.error) {
                            const errorElement = document.getElementById('card-errors');
                            errorElement.textContent = result.error.message;
                            errorElement.style.display = 'block';
                        } else {
                            const tokenInput = document.createElement('input');
                            tokenInput.setAttribute('type', 'hidden');
                            tokenInput.setAttribute('name', 'stripeToken');
                            tokenInput.setAttribute('value', result.token.id);
                            form.appendChild(tokenInput);
                            form.submit();
                        }
                    });
                } else {
                    form.submit();
                }
            });
            <?php endif; ?>
        });
    </script>
</body>
</html> 