<?php
/**
 * Session RSVP Management
 * View and manage RSVPs for individual event sessions
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$admin_id = $_SESSION['admin_id'];
$message = '';
$error = '';

// Get session ID from URL
$session_id = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;

if (!$session_id) {
    header("Location: events.php");
    exit();
}

// Get session and event details
try {
    $stmt = $pdo->prepare("
        SELECT es.*, e.title as event_title, e.id as event_id
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        WHERE es.id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading session: " . $e->getMessage();
}

// Get session RSVPs
try {
    $stmt = $pdo->prepare("
        SELECT sr.*, m.full_name, m.email, m.phone_number, m.first_name, m.last_name
        FROM session_rsvps sr
        JOIN members m ON sr.member_id = m.id
        WHERE sr.session_id = ?
        ORDER BY sr.response, m.full_name
    ");
    $stmt->execute([$session_id]);
    $rsvps = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error loading RSVPs: " . $e->getMessage();
    $rsvps = [];
}

// Get RSVP statistics
$stats = [
    'yes' => 0,
    'no' => 0,
    'maybe' => 0,
    'total' => count($rsvps)
];

foreach ($rsvps as $rsvp) {
    $stats[$rsvp['response']]++;
}

// Get members who RSVP'd to main event but not this session
try {
    $stmt = $pdo->prepare("
        SELECT m.id, m.full_name, m.email
        FROM event_rsvps er
        JOIN members m ON er.member_id = m.id
        WHERE er.event_id = ? AND er.response = 'yes'
        AND m.id NOT IN (
            SELECT member_id FROM session_rsvps WHERE session_id = ?
        )
        ORDER BY m.full_name
    ");
    $stmt->execute([$session['event_id'], $session_id]);
    $available_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $available_members = [];
}

// Handle manual RSVP addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_rsvp'])) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO session_rsvps (session_id, member_id, response, notes)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $session_id,
            $_POST['member_id'],
            $_POST['response'],
            $_POST['notes']
        ]);
        $message = "RSVP added successfully!";
        
        // Refresh the page to update the lists
        header("Location: session_rsvps.php?session_id=" . $session_id);
        exit();
    } catch (PDOException $e) {
        $error = "Error adding RSVP: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session RSVPs - <?php echo htmlspecialchars($session['session_title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/header.php'; ?>
        
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                    <li class="breadcrumb-item"><a href="event_sessions.php?event_id=<?php echo $session['event_id']; ?>">Sessions</a></li>
                    <li class="breadcrumb-item active">RSVPs</li>
                </ol>
            </nav>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Session Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h4>
                            <p class="text-muted mb-1">
                                <strong>Event:</strong> <?php echo htmlspecialchars($session['event_title']); ?>
                            </p>
                            <p class="text-muted mb-0">
                                <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?> - 
                                <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                <?php if ($session['location']): ?>
                                    | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if (!empty($available_members)): ?>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRsvpModal">
                                    <i class="bi bi-plus-circle"></i> Add RSVP
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RSVP Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo $stats['yes']; ?></h3>
                            <p class="mb-0">Attending</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo $stats['maybe']; ?></h3>
                            <p class="mb-0">Maybe</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-danger"><?php echo $stats['no']; ?></h3>
                            <p class="mb-0">Not Attending</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $stats['total']; ?></h3>
                            <p class="mb-0">Total RSVPs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RSVPs List -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-people"></i> Session RSVPs</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($rsvps)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">No RSVPs Yet</h5>
                            <p class="text-muted">No one has RSVP'd to this session yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Contact</th>
                                        <th>Response</th>
                                        <th>RSVP Date</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rsvps as $rsvp): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($rsvp['full_name']); ?></strong>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php if ($rsvp['email']): ?>
                                                        <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($rsvp['email']); ?><br>
                                                    <?php endif; ?>
                                                    <?php if ($rsvp['phone_number']): ?>
                                                        <i class="bi bi-phone"></i> <?php echo htmlspecialchars($rsvp['phone_number']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $badge_class = [
                                                    'yes' => 'bg-success',
                                                    'no' => 'bg-danger',
                                                    'maybe' => 'bg-warning'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $badge_class[$rsvp['response']]; ?>">
                                                    <?php echo ucfirst($rsvp['response']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y g:i A', strtotime($rsvp['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($rsvp['notes']): ?>
                                                    <small><?php echo htmlspecialchars($rsvp['notes']); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">No notes</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                                            onclick="editRsvp(<?php echo $rsvp['id']; ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="deleteRsvp(<?php echo $rsvp['id']; ?>)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Available Members -->
            <?php if (!empty($available_members)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="bi bi-person-plus"></i> Members Who Haven't RSVP'd to This Session</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">These members have RSVP'd to the main event but not to this specific session:</p>
                        <div class="row">
                            <?php foreach ($available_members as $member): ?>
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><?php echo htmlspecialchars($member['full_name']); ?></span>
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="quickRsvp(<?php echo $member['id']; ?>, '<?php echo htmlspecialchars($member['full_name']); ?>')">
                                            Add
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add RSVP Modal -->
    <div class="modal fade" id="addRsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Session RSVP</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Member *</label>
                            <select class="form-select" name="member_id" required>
                                <option value="">Select a member...</option>
                                <?php foreach ($available_members as $member): ?>
                                    <option value="<?php echo $member['id']; ?>">
                                        <?php echo htmlspecialchars($member['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Response *</label>
                            <select class="form-select" name="response" required>
                                <option value="yes">Yes - Attending</option>
                                <option value="maybe">Maybe</option>
                                <option value="no">No - Not Attending</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_rsvp" class="btn btn-primary">Add RSVP</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function quickRsvp(memberId, memberName) {
            // Set the member in the modal and show it
            const modal = new bootstrap.Modal(document.getElementById('addRsvpModal'));
            document.querySelector('select[name="member_id"]').value = memberId;
            modal.show();
        }

        function editRsvp(rsvpId) {
            // TODO: Implement edit functionality
            alert('Edit functionality coming soon!');
        }

        function deleteRsvp(rsvpId) {
            if (confirm('Are you sure you want to delete this RSVP?')) {
                // TODO: Implement delete functionality
                alert('Delete functionality coming soon!');
            }
        }

        // Export functions
        function exportRsvps() {
            window.location.href = 'export_session_rsvps.php?session_id=<?php echo $session_id; ?>';
        }

        function sendReminders() {
            if (confirm('Send reminder emails to all attendees?')) {
                // TODO: Implement reminder functionality
                alert('Reminder functionality coming soon!');
            }
        }
    </script>
</body>
</html>
