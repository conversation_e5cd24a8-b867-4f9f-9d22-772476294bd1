<?php
/**
 * Event Sessions Management
 * Manage multi-session events and workshops
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$admin_id = $_SESSION['admin_id'];
$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_session'])) {
        // Add new session
        try {
            $stmt = $pdo->prepare("
                INSERT INTO event_sessions 
                (event_id, session_title, session_description, session_number, start_datetime, end_datetime, 
                 location, max_attendees, instructor_name, instructor_bio, session_type, prerequisites, 
                 materials_needed, session_fee, is_mandatory) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $event_id,
                $_POST['session_title'],
                $_POST['session_description'],
                $_POST['session_number'],
                $_POST['start_datetime'],
                $_POST['end_datetime'],
                $_POST['location'],
                $_POST['max_attendees'] ?: null,
                $_POST['instructor_name'],
                $_POST['instructor_bio'],
                $_POST['session_type'],
                $_POST['prerequisites'],
                $_POST['materials_needed'],
                $_POST['session_fee'] ?: 0.00,
                isset($_POST['is_mandatory']) ? 1 : 0
            ]);
            
            $message = "Session added successfully!";
        } catch (PDOException $e) {
            $error = "Error adding session: " . $e->getMessage();
        }
    } elseif (isset($_POST['update_session'])) {
        // Update existing session
        try {
            $stmt = $pdo->prepare("
                UPDATE event_sessions SET 
                session_title = ?, session_description = ?, session_number = ?, start_datetime = ?, 
                end_datetime = ?, location = ?, max_attendees = ?, instructor_name = ?, instructor_bio = ?, 
                session_type = ?, prerequisites = ?, materials_needed = ?, session_fee = ?, is_mandatory = ?
                WHERE id = ? AND event_id = ?
            ");
            
            $stmt->execute([
                $_POST['session_title'],
                $_POST['session_description'],
                $_POST['session_number'],
                $_POST['start_datetime'],
                $_POST['end_datetime'],
                $_POST['location'],
                $_POST['max_attendees'] ?: null,
                $_POST['instructor_name'],
                $_POST['instructor_bio'],
                $_POST['session_type'],
                $_POST['prerequisites'],
                $_POST['materials_needed'],
                $_POST['session_fee'] ?: 0.00,
                isset($_POST['is_mandatory']) ? 1 : 0,
                $_POST['session_id'],
                $event_id
            ]);
            
            $message = "Session updated successfully!";
        } catch (PDOException $e) {
            $error = "Error updating session: " . $e->getMessage();
        }
    } elseif (isset($_POST['delete_session'])) {
        // Delete session
        try {
            $stmt = $pdo->prepare("DELETE FROM event_sessions WHERE id = ? AND event_id = ?");
            $stmt->execute([$_POST['session_id'], $event_id]);
            $message = "Session deleted successfully!";
        } catch (PDOException $e) {
            $error = "Error deleting session: " . $e->getMessage();
        }
    }
}

// Get all sessions for this event
try {
    $stmt = $pdo->prepare("
        SELECT es.*, 
               COUNT(sr.id) as rsvp_count,
               COUNT(CASE WHEN sr.response = 'yes' THEN 1 END) as yes_count
        FROM event_sessions es
        LEFT JOIN session_rsvps sr ON es.id = sr.session_id
        WHERE es.event_id = ?
        GROUP BY es.id
        ORDER BY es.session_number, es.start_datetime
    ");
    $stmt->execute([$event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error loading sessions: " . $e->getMessage();
    $sessions = [];
}

// Get session for editing if requested
$edit_session = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM event_sessions WHERE id = ? AND event_id = ?");
        $stmt->execute([$_GET['edit'], $event_id]);
        $edit_session = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error = "Error loading session for editing: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Sessions - <?php echo htmlspecialchars($event['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/header.php'; ?>
        
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                    <li class="breadcrumb-item active">Sessions - <?php echo htmlspecialchars($event['title']); ?></li>
                </ol>
            </nav>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Event Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1"><?php echo htmlspecialchars($event['title']); ?></h4>
                            <p class="text-muted mb-0">
                                <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($event['start_date'])); ?>
                                <?php if ($event['location']): ?>
                                    | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sessionModal">
                                <i class="bi bi-plus-circle"></i> Add Session
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sessions List -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-collection"></i> Event Sessions</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($sessions)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">No Sessions Yet</h5>
                            <p class="text-muted">Add sessions to create a multi-session event or workshop.</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sessionModal">
                                <i class="bi bi-plus-circle"></i> Add First Session
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Session Title</th>
                                        <th>Date & Time</th>
                                        <th>Type</th>
                                        <th>Instructor</th>
                                        <th>RSVPs</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sessions as $session): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $session['session_number']; ?></span>
                                                <?php if ($session['is_mandatory']): ?>
                                                    <span class="badge bg-warning ms-1">Required</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                                <?php if ($session['session_description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($session['session_description'], 0, 100)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?><br>
                                                    <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - 
                                                    <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo ucfirst($session['session_type']); ?></span>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($session['instructor_name'] ?: 'TBD'); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?php echo $session['yes_count']; ?> Yes</span>
                                                <small class="text-muted">(<?php echo $session['rsvp_count']; ?> total)</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?event_id=<?php echo $event_id; ?>&edit=<?php echo $session['id']; ?>" class="btn btn-outline-primary">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="session_rsvps.php?session_id=<?php echo $session['id']; ?>" class="btn btn-outline-info">
                                                        <i class="bi bi-people"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteSession(<?php echo $session['id']; ?>)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Session Modal -->
    <div class="modal fade" id="sessionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <?php echo $edit_session ? 'Edit Session' : 'Add New Session'; ?>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php if ($edit_session): ?>
                            <input type="hidden" name="session_id" value="<?php echo $edit_session['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Session Title *</label>
                                    <input type="text" class="form-control" name="session_title" 
                                           value="<?php echo htmlspecialchars($edit_session['session_title'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Session Number *</label>
                                    <input type="number" class="form-control" name="session_number" min="1"
                                           value="<?php echo $edit_session['session_number'] ?? (count($sessions) + 1); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="session_description" rows="3"><?php echo htmlspecialchars($edit_session['session_description'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Start Date & Time *</label>
                                    <input type="datetime-local" class="form-control" name="start_datetime" 
                                           value="<?php echo $edit_session ? date('Y-m-d\TH:i', strtotime($edit_session['start_datetime'])) : ''; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">End Date & Time *</label>
                                    <input type="datetime-local" class="form-control" name="end_datetime" 
                                           value="<?php echo $edit_session ? date('Y-m-d\TH:i', strtotime($edit_session['end_datetime'])) : ''; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Session Type</label>
                                    <select class="form-select" name="session_type">
                                        <option value="lecture" <?php echo ($edit_session['session_type'] ?? '') === 'lecture' ? 'selected' : ''; ?>>Lecture</option>
                                        <option value="workshop" <?php echo ($edit_session['session_type'] ?? '') === 'workshop' ? 'selected' : ''; ?>>Workshop</option>
                                        <option value="discussion" <?php echo ($edit_session['session_type'] ?? '') === 'discussion' ? 'selected' : ''; ?>>Discussion</option>
                                        <option value="practical" <?php echo ($edit_session['session_type'] ?? '') === 'practical' ? 'selected' : ''; ?>>Practical</option>
                                        <option value="break" <?php echo ($edit_session['session_type'] ?? '') === 'break' ? 'selected' : ''; ?>>Break</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Location</label>
                                    <input type="text" class="form-control" name="location" 
                                           value="<?php echo htmlspecialchars($edit_session['location'] ?? $event['location']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Instructor Name</label>
                                    <input type="text" class="form-control" name="instructor_name" 
                                           value="<?php echo htmlspecialchars($edit_session['instructor_name'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Max Attendees</label>
                                    <input type="number" class="form-control" name="max_attendees" min="1"
                                           value="<?php echo $edit_session['max_attendees'] ?? ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Instructor Bio</label>
                            <textarea class="form-control" name="instructor_bio" rows="2"><?php echo htmlspecialchars($edit_session['instructor_bio'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Prerequisites</label>
                            <textarea class="form-control" name="prerequisites" rows="2"><?php echo htmlspecialchars($edit_session['prerequisites'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Materials Needed</label>
                            <textarea class="form-control" name="materials_needed" rows="2"><?php echo htmlspecialchars($edit_session['materials_needed'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Session Fee ($)</label>
                                    <input type="number" class="form-control" name="session_fee" step="0.01" min="0"
                                           value="<?php echo $edit_session['session_fee'] ?? '0.00'; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" name="is_mandatory" 
                                               <?php echo ($edit_session['is_mandatory'] ?? 0) ? 'checked' : ''; ?>>
                                        <label class="form-check-label">Mandatory Session</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="<?php echo $edit_session ? 'update_session' : 'add_session'; ?>" class="btn btn-primary">
                            <?php echo $edit_session ? 'Update Session' : 'Add Session'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="session_id" id="deleteSessionId">
        <input type="hidden" name="delete_session" value="1">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteSession(sessionId) {
            if (confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
                document.getElementById('deleteSessionId').value = sessionId;
                document.getElementById('deleteForm').submit();
            }
        }

        // Auto-show modal if editing
        <?php if ($edit_session): ?>
        document.addEventListener('DOMContentLoaded', function() {
            new bootstrap.Modal(document.getElementById('sessionModal')).show();
        });
        <?php endif; ?>
    </script>
</body>
</html>
