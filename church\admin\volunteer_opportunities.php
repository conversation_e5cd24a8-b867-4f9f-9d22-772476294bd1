<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_opportunity'])) {
            // Add new volunteer opportunity
            $stmt = $pdo->prepare("
                INSERT INTO volunteer_opportunities 
                (title, description, category, required_skills, preferred_skills, time_commitment, 
                 schedule_type, start_date, end_date, location, contact_person_id, max_volunteers, 
                 min_age, background_check_required, training_required, training_description, 
                 status, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)
            ");
            
            $stmt->execute([
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['required_skills'] ? json_encode(explode(',', $_POST['required_skills'])) : null,
                $_POST['preferred_skills'] ? json_encode(explode(',', $_POST['preferred_skills'])) : null,
                $_POST['time_commitment'],
                $_POST['schedule_type'],
                $_POST['start_date'] ?: null,
                $_POST['end_date'] ?: null,
                $_POST['location'],
                $_POST['contact_person_id'],
                $_POST['max_volunteers'] ?: null,
                $_POST['min_age'] ?: null,
                isset($_POST['background_check_required']) ? 1 : 0,
                isset($_POST['training_required']) ? 1 : 0,
                $_POST['training_description'],
                $_SESSION['admin_id']
            ]);
            
            $message = "Volunteer opportunity created successfully!";
        }
        
        if (isset($_POST['update_opportunity'])) {
            // Update existing opportunity
            $stmt = $pdo->prepare("
                UPDATE volunteer_opportunities 
                SET title = ?, description = ?, category = ?, required_skills = ?, preferred_skills = ?, 
                    time_commitment = ?, schedule_type = ?, start_date = ?, end_date = ?, location = ?, 
                    contact_person_id = ?, max_volunteers = ?, min_age = ?, background_check_required = ?, 
                    training_required = ?, training_description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['required_skills'] ? json_encode(explode(',', $_POST['required_skills'])) : null,
                $_POST['preferred_skills'] ? json_encode(explode(',', $_POST['preferred_skills'])) : null,
                $_POST['time_commitment'],
                $_POST['schedule_type'],
                $_POST['start_date'] ?: null,
                $_POST['end_date'] ?: null,
                $_POST['location'],
                $_POST['contact_person_id'],
                $_POST['max_volunteers'] ?: null,
                $_POST['min_age'] ?: null,
                isset($_POST['background_check_required']) ? 1 : 0,
                isset($_POST['training_required']) ? 1 : 0,
                $_POST['training_description'],
                $_POST['opportunity_id']
            ]);
            
            $message = "Volunteer opportunity updated successfully!";
        }
        
        if (isset($_POST['delete_opportunity'])) {
            // Delete opportunity
            $stmt = $pdo->prepare("DELETE FROM volunteer_opportunities WHERE id = ?");
            $stmt->execute([$_POST['opportunity_id']]);
            
            $message = "Volunteer opportunity deleted successfully!";
        }
        
        if (isset($_POST['update_application_status'])) {
            // Update application status
            $stmt = $pdo->prepare("
                UPDATE volunteer_applications 
                SET status = ?, reviewed_by = ?, reviewed_at = CURRENT_TIMESTAMP, review_notes = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['status'],
                $_SESSION['admin_id'],
                $_POST['review_notes'],
                $_POST['application_id']
            ]);
            
            $message = "Application status updated successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get all volunteer opportunities
try {
    $stmt = $pdo->prepare("
        SELECT vo.*, m.full_name as contact_name,
               COUNT(DISTINCT va.id) as application_count,
               COUNT(DISTINCT CASE WHEN va.status = 'approved' THEN va.id END) as approved_count
        FROM volunteer_opportunities vo
        LEFT JOIN members m ON vo.contact_person_id = m.id
        LEFT JOIN volunteer_applications va ON vo.id = va.opportunity_id
        GROUP BY vo.id
        ORDER BY vo.created_at DESC
    ");
    $stmt->execute();
    $opportunities = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $opportunities = [];
    $error = "Error loading opportunities: " . $e->getMessage();
}

// Get all members for contact person dropdown
try {
    $stmt = $pdo->prepare("SELECT id, full_name FROM members WHERE status = 'active' ORDER BY full_name");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $members = [];
}

// Get pending applications
try {
    $stmt = $pdo->prepare("
        SELECT va.*, vo.title as opportunity_title, m.full_name as applicant_name, m.email as applicant_email
        FROM volunteer_applications va
        JOIN volunteer_opportunities vo ON va.opportunity_id = vo.id
        JOIN members m ON va.member_id = m.id
        WHERE va.status = 'pending'
        ORDER BY va.created_at DESC
    ");
    $stmt->execute();
    $pending_applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $pending_applications = [];
}

// Set page variables
$page_title = 'Volunteer Opportunities';
$page_header = 'Volunteer Opportunities Management';
$page_description = 'Create and manage volunteer opportunities and applications';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-person-workspace"></i> Volunteer Opportunities</h2>
                    <p class="text-muted">Create and manage volunteer opportunities for your organization</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOpportunityModal">
                    <i class="bi bi-plus-circle"></i> Add Opportunity
                </button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-briefcase fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo count($opportunities); ?></h3>
                                    <small>Total Opportunities</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-clock-history fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo count($pending_applications); ?></h3>
                                    <small>Pending Applications</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo array_sum(array_column($opportunities, 'approved_count')); ?></h3>
                                    <small>Active Volunteers</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo array_sum(array_column($opportunities, 'application_count')); ?></h3>
                                    <small>Total Applications</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Applications Section -->
            <?php if (!empty($pending_applications)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history"></i> Pending Applications (<?php echo count($pending_applications); ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Applicant</th>
                                    <th>Opportunity</th>
                                    <th>Applied Date</th>
                                    <th>Message</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_applications as $app): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($app['applicant_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($app['applicant_email']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($app['opportunity_title']); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($app['created_at'])); ?></td>
                                    <td>
                                        <?php if ($app['application_message']): ?>
                                            <small><?php echo htmlspecialchars(substr($app['application_message'], 0, 100)); ?>...</small>
                                        <?php else: ?>
                                            <em class="text-muted">No message</em>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="updateApplicationStatus(<?php echo $app['id']; ?>, 'approved')">
                                            <i class="bi bi-check"></i> Approve
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="updateApplicationStatus(<?php echo $app['id']; ?>, 'rejected')">
                                            <i class="bi bi-x"></i> Reject
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Volunteer Opportunities List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-briefcase"></i> All Volunteer Opportunities</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($opportunities)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">No Volunteer Opportunities</h4>
                            <p class="text-muted">Start by creating your first volunteer opportunity.</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOpportunityModal">
                                <i class="bi bi-plus-circle"></i> Create First Opportunity
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Contact Person</th>
                                        <th>Applications</th>
                                        <th>Schedule</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($opportunities as $opp): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($opp['title']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($opp['description'], 0, 100)); ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo ucfirst($opp['category']); ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($opp['contact_name']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $opp['application_count']; ?> Total</span>
                                            <span class="badge bg-success"><?php echo $opp['approved_count']; ?> Approved</span>
                                        </td>
                                        <td>
                                            <small>
                                                <?php echo ucfirst(str_replace('_', ' ', $opp['schedule_type'])); ?><br>
                                                <?php if ($opp['start_date']): ?>
                                                    <?php echo date('M j, Y', strtotime($opp['start_date'])); ?>
                                                <?php endif; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $opp['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($opp['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editOpportunity(<?php echo $opp['id']; ?>)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewApplications(<?php echo $opp['id']; ?>)">
                                                    <i class="bi bi-people"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteOpportunity(<?php echo $opp['id']; ?>, '<?php echo htmlspecialchars($opp['title']); ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Opportunity Modal -->
<div class="modal fade" id="addOpportunityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Add Volunteer Opportunity</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="ministry">Ministry</option>
                                    <option value="outreach">Outreach</option>
                                    <option value="administration">Administration</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="events">Events</option>
                                    <option value="children">Children's Ministry</option>
                                    <option value="youth">Youth Ministry</option>
                                    <option value="music">Music & Worship</option>
                                    <option value="technology">Technology</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control" name="description" rows="3" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_person_id" class="form-label">Contact Person *</label>
                                <select class="form-select" name="contact_person_id" required>
                                    <option value="">Select Contact Person</option>
                                    <?php foreach ($members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>"><?php echo htmlspecialchars($member['full_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="schedule_type" class="form-label">Schedule Type *</label>
                                <select class="form-select" name="schedule_type" required>
                                    <option value="one_time">One Time</option>
                                    <option value="recurring">Recurring</option>
                                    <option value="flexible">Flexible</option>
                                    <option value="ongoing">Ongoing</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_volunteers" class="form-label">Max Volunteers</label>
                                <input type="number" class="form-control" name="max_volunteers" min="1">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" name="location">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="time_commitment" class="form-label">Time Commitment</label>
                                <input type="text" class="form-control" name="time_commitment" placeholder="e.g., 2 hours per week">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="required_skills" class="form-label">Required Skills</label>
                                <input type="text" class="form-control" name="required_skills" placeholder="Comma-separated skills">
                                <small class="form-text text-muted">Enter skills separated by commas</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="preferred_skills" class="form-label">Preferred Skills</label>
                                <input type="text" class="form-control" name="preferred_skills" placeholder="Comma-separated skills">
                                <small class="form-text text-muted">Enter skills separated by commas</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="min_age" class="form-label">Minimum Age</label>
                                <input type="number" class="form-control" name="min_age" min="0" max="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" name="background_check_required" id="background_check">
                                <label class="form-check-label" for="background_check">
                                    Background Check Required
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="training_required" id="training_required">
                        <label class="form-check-label" for="training_required">
                            Training Required
                        </label>
                    </div>

                    <div class="mb-3">
                        <label for="training_description" class="form-label">Training Description</label>
                        <textarea class="form-control" name="training_description" rows="2" placeholder="Describe any required training"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_opportunity" class="btn btn-primary">Create Opportunity</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Application Status Update Modal -->
<div class="modal fade" id="applicationStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="applicationStatusForm">
                <div class="modal-header">
                    <h5 class="modal-title">Update Application Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="application_id" id="application_id">
                    <input type="hidden" name="status" id="application_status">

                    <div class="mb-3">
                        <label for="review_notes" class="form-label">Review Notes</label>
                        <textarea class="form-control" name="review_notes" rows="3" placeholder="Add notes about your decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_application_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Opportunity Modal -->
<div class="modal fade" id="editOpportunityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" id="editOpportunityForm">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Volunteer Opportunity</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="opportunity_id" id="editOpportunityId">
                    <input type="hidden" name="update_opportunity" value="1">

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="editTitle" class="form-label">Title *</label>
                            <input type="text" class="form-control" name="title" id="editTitle" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="editStatus" class="form-label">Status</label>
                            <select class="form-select" name="status" id="editStatus">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="filled">Filled</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editDescription" class="form-label">Description *</label>
                        <textarea class="form-control" name="description" id="editDescription" rows="4" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editCategory" class="form-label">Category</label>
                            <select class="form-select" name="category" id="editCategory">
                                <option value="">Select Category</option>
                                <option value="worship">Worship</option>
                                <option value="children">Children's Ministry</option>
                                <option value="youth">Youth Ministry</option>
                                <option value="outreach">Outreach</option>
                                <option value="administration">Administration</option>
                                <option value="facilities">Facilities</option>
                                <option value="hospitality">Hospitality</option>
                                <option value="media">Media/Technology</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editSchedule" class="form-label">Schedule</label>
                            <input type="text" class="form-control" name="schedule_info" id="editSchedule" placeholder="e.g., Sundays 9:00 AM">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="editContactPerson" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" name="contact_person" id="editContactPerson">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="editContactEmail" class="form-label">Contact Email</label>
                            <input type="email" class="form-control" name="contact_email" id="editContactEmail">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="editContactPhone" class="form-label">Contact Phone</label>
                            <input type="tel" class="form-control" name="contact_phone" id="editContactPhone">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editRequirements" class="form-label">Requirements</label>
                        <textarea class="form-control" name="requirements" id="editRequirements" rows="3" placeholder="List any specific requirements or qualifications"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Opportunity</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Applications Modal -->
<div class="modal fade" id="applicationsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applicationsModalLabel">Applications</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Applicant</th>
                                <th>Email</th>
                                <th>Applied Date</th>
                                <th>Message</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="applicationsTableBody">
                            <!-- Applications will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function updateApplicationStatus(applicationId, status) {
    document.getElementById('application_id').value = applicationId;
    document.getElementById('application_status').value = status;

    const modal = new bootstrap.Modal(document.getElementById('applicationStatusModal'));
    modal.show();
}

function editOpportunity(opportunityId) {
    // Fetch opportunity data and populate edit modal
    fetch(`get_opportunity.php?id=${opportunityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const opp = data.opportunity;

                // Populate edit form
                document.getElementById('editOpportunityId').value = opp.id;
                document.getElementById('editTitle').value = opp.title;
                document.getElementById('editDescription').value = opp.description;
                document.getElementById('editCategory').value = opp.category;
                document.getElementById('editContactPerson').value = opp.contact_person;
                document.getElementById('editContactEmail').value = opp.contact_email;
                document.getElementById('editContactPhone').value = opp.contact_phone;
                document.getElementById('editSchedule').value = opp.schedule_info;
                document.getElementById('editRequirements').value = opp.requirements;
                document.getElementById('editStatus').value = opp.status;

                // Show edit modal
                const modal = new bootstrap.Modal(document.getElementById('editOpportunityModal'));
                modal.show();
            } else {
                alert('Error loading opportunity data: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading opportunity data');
        });
}

function viewApplications(opportunityId) {
    // Fetch applications for this opportunity
    fetch(`get_opportunity_applications.php?opportunity_id=${opportunityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const applications = data.applications;
                const opportunityTitle = data.opportunity_title;

                // Update modal title
                document.getElementById('applicationsModalLabel').textContent = `Applications for: ${opportunityTitle}`;

                // Populate applications table
                const tbody = document.getElementById('applicationsTableBody');
                tbody.innerHTML = '';

                if (applications.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center">No applications found</td></tr>';
                } else {
                    applications.forEach(app => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${app.applicant_name}</td>
                            <td>${app.applicant_email}</td>
                            <td>${new Date(app.applied_date).toLocaleDateString()}</td>
                            <td>${app.message || 'No message'}</td>
                            <td>
                                <select class="form-select form-select-sm" onchange="updateApplicationStatus(${app.id}, this.value)">
                                    <option value="pending" ${app.status === 'pending' ? 'selected' : ''}>Pending</option>
                                    <option value="approved" ${app.status === 'approved' ? 'selected' : ''}>Approved</option>
                                    <option value="rejected" ${app.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                                </select>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                }

                // Show applications modal
                const modal = new bootstrap.Modal(document.getElementById('applicationsModal'));
                modal.show();
            } else {
                alert('Error loading applications: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading applications');
        });
}

function deleteOpportunity(opportunityId, title) {
    if (confirm(`Are you sure you want to delete the opportunity "${title}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="opportunity_id" value="${opportunityId}">
            <input type="hidden" name="delete_opportunity" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function updateApplicationStatus(applicationId, status) {
    // Update application status via AJAX
    const formData = new FormData();
    formData.append('application_id', applicationId);
    formData.append('status', status);
    formData.append('update_application_status', '1');

    fetch('volunteer_opportunities.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            Application status updated successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the modal body
        const modalBody = document.querySelector('#applicationsModal .modal-body');
        modalBody.insertBefore(alertDiv, modalBody.firstChild);

        // Auto-hide alert after 3 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating application status');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
