<?php
/**
 * Database Schema Checker
 * Check what tables and columns exist in the database
 */

require_once '../config.php';

echo "<h2>Database Schema Check</h2>";

// Check if prayer_requests table exists
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'prayer_requests'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<h3>✅ prayer_requests table exists</h3>";
        
        // Get column information
        $stmt = $pdo->query("DESCRIBE prayer_requests");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Columns in prayer_requests table:</h4>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<h3>❌ prayer_requests table does NOT exist</h3>";
        echo "<p>The table needs to be created. Let me create it now...</p>";
        
        // Create the table
        $sql = "CREATE TABLE prayer_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(50) DEFAULT 'personal',
            privacy_level VARCHAR(20) DEFAULT 'private',
            status VARCHAR(20) DEFAULT 'active',
            is_urgent BOOLEAN DEFAULT FALSE,
            is_anonymous BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_status (status),
            INDEX idx_privacy_level (privacy_level),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )";
        
        $pdo->exec($sql);
        echo "<p>✅ prayer_requests table created successfully!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Check if prayer_responses table exists
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'prayer_responses'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<h3>✅ prayer_responses table exists</h3>";
    } else {
        echo "<h3>❌ prayer_responses table does NOT exist</h3>";
        echo "<p>Creating prayer_responses table...</p>";
        
        // Create the table
        $sql = "CREATE TABLE prayer_responses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prayer_request_id INT NOT NULL,
            member_id INT NOT NULL,
            response_type VARCHAR(50) DEFAULT 'prayed',
            comment TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_prayer_request_id (prayer_request_id),
            INDEX idx_member_id (member_id),
            FOREIGN KEY (prayer_request_id) REFERENCES prayer_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )";
        
        $pdo->exec($sql);
        echo "<p>✅ prayer_responses table created successfully!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Check if family_relationships table exists
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'family_relationships'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<h3>✅ family_relationships table exists</h3>";
    } else {
        echo "<h3>❌ family_relationships table does NOT exist</h3>";
        echo "<p>Creating family_relationships table...</p>";
        
        // Create the table
        $sql = "CREATE TABLE family_relationships (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            related_member_id INT NOT NULL,
            relationship_type VARCHAR(50) NOT NULL,
            can_manage_profile BOOLEAN DEFAULT FALSE,
            can_rsvp_for_member BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_related_member_id (related_member_id),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (related_member_id) REFERENCES members(id) ON DELETE CASCADE,
            UNIQUE KEY unique_relationship (member_id, related_member_id)
        )";
        
        $pdo->exec($sql);
        echo "<p>✅ family_relationships table created successfully!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Check if members table has the new columns
try {
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existing_columns = array_column($columns, 'Field');
    $new_columns = [
        'bio', 'interests', 'emergency_contact_name', 'emergency_contact_phone', 
        'emergency_contact_relationship', 'medical_conditions', 'allergies', 
        'preferred_communication', 'anniversary_date', 'baptism_date', 
        'volunteer_interests', 'availability_schedule', 'social_media_links', 
        'privacy_settings', 'notification_settings', 'profile_photo_path', 'cover_photo_path'
    ];
    
    echo "<h3>Members table column check:</h3>";
    $missing_columns = [];
    foreach ($new_columns as $column) {
        if (in_array($column, $existing_columns)) {
            echo "<p>✅ $column exists</p>";
        } else {
            echo "<p>❌ $column missing</p>";
            $missing_columns[] = $column;
        }
    }
    
    // Add missing columns
    if (!empty($missing_columns)) {
        echo "<h4>Adding missing columns to members table:</h4>";
        
        $alter_statements = [
            'bio' => "ALTER TABLE members ADD COLUMN bio TEXT",
            'interests' => "ALTER TABLE members ADD COLUMN interests TEXT",
            'emergency_contact_name' => "ALTER TABLE members ADD COLUMN emergency_contact_name VARCHAR(255)",
            'emergency_contact_phone' => "ALTER TABLE members ADD COLUMN emergency_contact_phone VARCHAR(20)",
            'emergency_contact_relationship' => "ALTER TABLE members ADD COLUMN emergency_contact_relationship VARCHAR(100)",
            'medical_conditions' => "ALTER TABLE members ADD COLUMN medical_conditions TEXT",
            'allergies' => "ALTER TABLE members ADD COLUMN allergies TEXT",
            'preferred_communication' => "ALTER TABLE members ADD COLUMN preferred_communication VARCHAR(20) DEFAULT 'email'",
            'anniversary_date' => "ALTER TABLE members ADD COLUMN anniversary_date DATE",
            'baptism_date' => "ALTER TABLE members ADD COLUMN baptism_date DATE",
            'volunteer_interests' => "ALTER TABLE members ADD COLUMN volunteer_interests TEXT",
            'availability_schedule' => "ALTER TABLE members ADD COLUMN availability_schedule TEXT",
            'social_media_links' => "ALTER TABLE members ADD COLUMN social_media_links JSON",
            'privacy_settings' => "ALTER TABLE members ADD COLUMN privacy_settings JSON",
            'notification_settings' => "ALTER TABLE members ADD COLUMN notification_settings JSON",
            'profile_photo_path' => "ALTER TABLE members ADD COLUMN profile_photo_path VARCHAR(255)",
            'cover_photo_path' => "ALTER TABLE members ADD COLUMN cover_photo_path VARCHAR(255)"
        ];
        
        foreach ($missing_columns as $column) {
            if (isset($alter_statements[$column])) {
                try {
                    $pdo->exec($alter_statements[$column]);
                    echo "<p>✅ Added $column to members table</p>";
                } catch (PDOException $e) {
                    echo "<p>❌ Error adding $column: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Error checking members table: " . $e->getMessage() . "</p>";
}

echo "<h3>✅ Database check and fixes completed!</h3>";
echo "<p><a href='prayer_requests.php'>Try Prayer Requests again</a></p>";
echo "<p><a href='family_management.php'>Try Family Management</a></p>";
echo "<p><a href='profile.php'>Try Enhanced Profile</a></p>";
?>
