<?php
/**
 * Advanced Features Database Migration
 * Safely applies the new database schema for enhanced event management and member portal
 * 
 * This script will:
 * 1. Check existing database structure
 * 2. Apply new tables and columns safely
 * 3. Preserve existing data
 * 4. Create necessary indexes and views
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$migration_log = [];
$errors = [];

function log_migration($message, $type = 'info') {
    global $migration_log;
    $migration_log[] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
}

function execute_sql($pdo, $sql, $description) {
    global $errors;
    try {
        $pdo->exec($sql);
        log_migration("✓ $description", 'success');
        return true;
    } catch (PDOException $e) {
        $error_msg = "✗ $description - Error: " . $e->getMessage();
        log_migration($error_msg, 'error');
        $errors[] = $error_msg;
        return false;
    }
}

function column_exists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function table_exists($pdo, $table) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    log_migration("Starting Advanced Features Database Migration", 'info');
    
    try {
        $pdo->beginTransaction();
        
        // ============================================================================
        // STEP 1: CREATE NEW TABLES
        // ============================================================================
        
        log_migration("Creating new tables for advanced features...", 'info');
        
        // Event Sessions Table
        if (!table_exists($pdo, 'event_sessions')) {
            $sql = "CREATE TABLE `event_sessions` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_id` int(11) NOT NULL,
              `session_title` varchar(255) NOT NULL,
              `session_description` text DEFAULT NULL,
              `session_number` int(11) NOT NULL DEFAULT 1,
              `start_datetime` datetime NOT NULL,
              `end_datetime` datetime NOT NULL,
              `location` varchar(255) DEFAULT NULL,
              `max_attendees` int(11) DEFAULT NULL,
              `instructor_name` varchar(255) DEFAULT NULL,
              `instructor_bio` text DEFAULT NULL,
              `session_type` enum('lecture','workshop','discussion','practical','break') NOT NULL DEFAULT 'lecture',
              `prerequisites` text DEFAULT NULL,
              `materials_needed` text DEFAULT NULL,
              `session_fee` decimal(10,2) DEFAULT 0.00,
              `is_mandatory` tinyint(1) NOT NULL DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_event_id` (`event_id`),
              KEY `idx_session_number` (`session_number`),
              KEY `idx_start_datetime` (`start_datetime`),
              FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created event_sessions table");
        }
        
        // Session RSVPs Table
        if (!table_exists($pdo, 'session_rsvps')) {
            $sql = "CREATE TABLE `session_rsvps` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `session_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `response` enum('yes','no','maybe') NOT NULL DEFAULT 'yes',
              `notes` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_session_member` (`session_id`, `member_id`),
              KEY `idx_session_id` (`session_id`),
              KEY `idx_member_id` (`member_id`),
              FOREIGN KEY (`session_id`) REFERENCES `event_sessions` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created session_rsvps table");
        }
        
        // Event Guests Table
        if (!table_exists($pdo, 'event_guests')) {
            $sql = "CREATE TABLE `event_guests` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_id` int(11) NOT NULL,
              `rsvp_id` int(11) DEFAULT NULL,
              `guest_name` varchar(255) NOT NULL,
              `guest_email` varchar(255) DEFAULT NULL,
              `guest_phone` varchar(20) DEFAULT NULL,
              `age_group` enum('child','teen','adult','senior') DEFAULT 'adult',
              `relationship_to_member` varchar(100) DEFAULT NULL,
              `dietary_restrictions` text DEFAULT NULL,
              `special_needs` text DEFAULT NULL,
              `emergency_contact_name` varchar(255) DEFAULT NULL,
              `emergency_contact_phone` varchar(20) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_event_id` (`event_id`),
              KEY `idx_rsvp_id` (`rsvp_id`),
              KEY `idx_guest_email` (`guest_email`),
              FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created event_guests table");
        }
        
        // Carpooling Tables
        if (!table_exists($pdo, 'event_carpools')) {
            $sql = "CREATE TABLE `event_carpools` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_id` int(11) NOT NULL,
              `driver_member_id` int(11) NOT NULL,
              `departure_location` varchar(255) NOT NULL,
              `departure_time` datetime NOT NULL,
              `available_seats` int(11) NOT NULL DEFAULT 1,
              `return_time` datetime DEFAULT NULL,
              `contact_info` varchar(255) DEFAULT NULL,
              `notes` text DEFAULT NULL,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_event_id` (`event_id`),
              KEY `idx_driver_member_id` (`driver_member_id`),
              FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`driver_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created event_carpools table");
        }
        
        if (!table_exists($pdo, 'carpool_passengers')) {
            $sql = "CREATE TABLE `carpool_passengers` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `carpool_id` int(11) NOT NULL,
              `passenger_member_id` int(11) NOT NULL,
              `pickup_location` varchar(255) DEFAULT NULL,
              `special_instructions` text DEFAULT NULL,
              `status` enum('requested','confirmed','cancelled') NOT NULL DEFAULT 'requested',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_carpool_passenger` (`carpool_id`, `passenger_member_id`),
              KEY `idx_carpool_id` (`carpool_id`),
              KEY `idx_passenger_member_id` (`passenger_member_id`),
              FOREIGN KEY (`carpool_id`) REFERENCES `event_carpools` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`passenger_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created carpool_passengers table");
        }
        
        // Reminder System Tables
        if (!table_exists($pdo, 'reminder_templates')) {
            $sql = "CREATE TABLE `reminder_templates` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `description` text DEFAULT NULL,
              `event_type` varchar(50) DEFAULT NULL,
              `days_before_event` int(11) NOT NULL,
              `reminder_type` enum('email','sms','both') NOT NULL DEFAULT 'email',
              `subject_template` varchar(255) NOT NULL,
              `message_template` text NOT NULL,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_by` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_event_type` (`event_type`),
              KEY `idx_days_before` (`days_before_event`),
              FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created reminder_templates table");
        }
        
        if (!table_exists($pdo, 'scheduled_reminders')) {
            $sql = "CREATE TABLE `scheduled_reminders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_id` int(11) NOT NULL,
              `template_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `reminder_type` enum('email','sms','both') NOT NULL,
              `scheduled_datetime` datetime NOT NULL,
              `sent_datetime` datetime DEFAULT NULL,
              `status` enum('scheduled','sent','failed','cancelled') NOT NULL DEFAULT 'scheduled',
              `failure_reason` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_event_id` (`event_id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_scheduled_datetime` (`scheduled_datetime`),
              KEY `idx_status` (`status`),
              FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`template_id`) REFERENCES `reminder_templates` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created scheduled_reminders table");
        }
        
        // ============================================================================
        // STEP 2: ENHANCE EXISTING TABLES
        // ============================================================================
        
        log_migration("Enhancing existing tables with new columns...", 'info');
        
        // Enhance event_rsvps table
        $rsvp_columns = [
            'dietary_restrictions' => "ADD COLUMN `dietary_restrictions` text DEFAULT NULL COMMENT 'Dietary restrictions and allergies'",
            'special_needs' => "ADD COLUMN `special_needs` text DEFAULT NULL COMMENT 'Accessibility and special needs'",
            'transportation_needed' => "ADD COLUMN `transportation_needed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Needs transportation/carpooling'",
            'can_provide_transportation' => "ADD COLUMN `can_provide_transportation` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can provide rides to others'",
            'vehicle_capacity' => "ADD COLUMN `vehicle_capacity` int(11) DEFAULT NULL COMMENT 'Number of people can transport'",
            'emergency_contact_name' => "ADD COLUMN `emergency_contact_name` varchar(255) DEFAULT NULL",
            'emergency_contact_phone' => "ADD COLUMN `emergency_contact_phone` varchar(20) DEFAULT NULL",
            'additional_info' => "ADD COLUMN `additional_info` text DEFAULT NULL COMMENT 'Any additional information'"
        ];
        
        foreach ($rsvp_columns as $column => $sql_part) {
            if (!column_exists($pdo, 'event_rsvps', $column)) {
                execute_sql($pdo, "ALTER TABLE `event_rsvps` $sql_part", "Added $column to event_rsvps");
            }
        }

        // Continue with remaining tables in next migration phase
        log_migration("Phase 1 migration completed successfully! Run Phase 2 for remaining features.", 'success');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        log_migration("Migration failed: " . $e->getMessage(), 'error');
        $errors[] = "Migration failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Features Migration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-database-gear"></i> Advanced Features Database Migration</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($migration_log)): ?>
                            <div class="alert alert-info">
                                <h5><i class="bi bi-info-circle"></i> Ready to Migrate</h5>
                                <p>This migration will add advanced features to your organization management system:</p>
                                <ul>
                                    <li><strong>Multi-Session Events & Workshops</strong> - Support for events with multiple sessions</li>
                                    <li><strong>Enhanced RSVP System</strong> - Guest registration, dietary restrictions, carpooling</li>
                                    <li><strong>Automated Reminder Sequences</strong> - Customizable email/SMS reminders</li>
                                    <li><strong>Family Member Management</strong> - Link family relationships</li>
                                    <li><strong>Prayer Request System</strong> - Personal prayer tracking</li>
                                    <li><strong>Skills & Volunteer Matching</strong> - Member skills and volunteer opportunities</li>
                                    <li><strong>Enhanced Member Portal</strong> - Photo uploads, celebrations, achievements</li>
                                </ul>
                                <p class="mb-0"><strong>Note:</strong> This migration is safe and will not affect existing data.</p>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    <i class="bi bi-play-circle"></i> Run Migration
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="migration-log">
                                <h5>Migration Log</h5>
                                <?php foreach ($migration_log as $entry): ?>
                                    <div class="alert alert-<?php echo $entry['type'] === 'error' ? 'danger' : ($entry['type'] === 'success' ? 'success' : 'info'); ?> py-2">
                                        <small class="text-muted"><?php echo $entry['timestamp']; ?></small><br>
                                        <?php echo htmlspecialchars($entry['message']); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($errors)): ?>
                                <div class="alert alert-success">
                                    <h5><i class="bi bi-check-circle"></i> Migration Completed Successfully!</h5>
                                    <p>All advanced features have been added to your database. You can now:</p>
                                    <ul>
                                        <li>Create multi-session events and workshops</li>
                                        <li>Set up automated reminder sequences</li>
                                        <li>Enable enhanced RSVP features with guest registration</li>
                                        <li>Configure family relationships and member portal enhancements</li>
                                    </ul>
                                    <a href="dashboard.php" class="btn btn-success">
                                        <i class="bi bi-house"></i> Return to Dashboard
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5><i class="bi bi-exclamation-triangle"></i> Migration Completed with Errors</h5>
                                    <p>Some parts of the migration failed. Please review the log above and contact support if needed.</p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
