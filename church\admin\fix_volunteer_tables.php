<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>Fixing Volunteer Opportunities Tables</h2>";

try {
    // Check if volunteer_opportunities table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'volunteer_opportunities'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p>Creating volunteer_opportunities table...</p>";
        
        // Create the table with all required columns
        $sql = "CREATE TABLE `volunteer_opportunities` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text NOT NULL,
            `category` varchar(100) DEFAULT NULL,
            `contact_person` varchar(255) DEFAULT NULL,
            `contact_email` varchar(255) DEFAULT NULL,
            `contact_phone` varchar(20) DEFAULT NULL,
            `schedule_info` varchar(255) DEFAULT NULL,
            `requirements` text DEFAULT NULL,
            `status` enum('active','inactive','filled') NOT NULL DEFAULT 'active',
            `contact_person_id` int(11) DEFAULT NULL,
            `required_skills` text DEFAULT NULL,
            `preferred_skills` text DEFAULT NULL,
            `time_commitment` varchar(255) DEFAULT NULL,
            `schedule_type` enum('one_time','recurring','flexible','ongoing') NOT NULL DEFAULT 'one_time',
            `start_date` date DEFAULT NULL,
            `end_date` date DEFAULT NULL,
            `location` varchar(255) DEFAULT NULL,
            `max_volunteers` int(11) DEFAULT NULL,
            `min_age` int(11) DEFAULT NULL,
            `background_check_required` tinyint(1) NOT NULL DEFAULT 0,
            `training_required` tinyint(1) NOT NULL DEFAULT 0,
            `training_description` text DEFAULT NULL,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `idx_category` (`category`),
            KEY `idx_status` (`status`),
            KEY `idx_contact_person_id` (`contact_person_id`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ volunteer_opportunities table created successfully!</p>";
    } else {
        echo "<p>volunteer_opportunities table exists. Checking columns...</p>";
        
        // Get existing columns
        $stmt = $pdo->query("DESCRIBE volunteer_opportunities");
        $existingColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        echo "<p>Existing columns: " . implode(', ', $existingColumns) . "</p>";
        
        // Define required columns
        $requiredColumns = [
            'contact_person' => "ADD COLUMN `contact_person` varchar(255) DEFAULT NULL",
            'contact_email' => "ADD COLUMN `contact_email` varchar(255) DEFAULT NULL", 
            'contact_phone' => "ADD COLUMN `contact_phone` varchar(20) DEFAULT NULL",
            'schedule_info' => "ADD COLUMN `schedule_info` varchar(255) DEFAULT NULL",
            'requirements' => "ADD COLUMN `requirements` text DEFAULT NULL"
        ];
        
        // Add missing columns
        foreach ($requiredColumns as $column => $sql) {
            if (!in_array($column, $existingColumns)) {
                try {
                    $pdo->exec("ALTER TABLE volunteer_opportunities $sql");
                    echo "<p style='color: green;'>✅ Added column: $column</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ Error adding column $column: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ Column $column already exists</p>";
            }
        }
    }
    
    // Check if volunteer_applications table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'volunteer_applications'");
    $appTableExists = $stmt->rowCount() > 0;

    if (!$appTableExists) {
        echo "<p>Creating volunteer_applications table...</p>";

        $sql = "CREATE TABLE `volunteer_applications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `opportunity_id` int(11) NOT NULL,
            `member_id` int(11) NOT NULL,
            `message` text DEFAULT NULL,
            `applied_date` timestamp NOT NULL DEFAULT current_timestamp(),
            `status` enum('pending','approved','rejected','withdrawn') NOT NULL DEFAULT 'pending',
            `reviewed_by` int(11) DEFAULT NULL,
            `reviewed_at` datetime DEFAULT NULL,
            `review_notes` text DEFAULT NULL,
            `application_message` text DEFAULT NULL,
            `availability` text DEFAULT NULL,
            `relevant_experience` text DEFAULT NULL,
            `start_date` date DEFAULT NULL,
            `end_date` date DEFAULT NULL,
            `hours_committed` decimal(5,2) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_opportunity_member` (`opportunity_id`, `member_id`),
            KEY `idx_opportunity_id` (`opportunity_id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ volunteer_applications table created successfully!</p>";

        // Add some sample applications
        echo "<p>Adding sample volunteer applications...</p>";

        // First, get some opportunity and member IDs
        $oppStmt = $pdo->query("SELECT id FROM volunteer_opportunities LIMIT 2");
        $opportunities = $oppStmt->fetchAll(PDO::FETCH_COLUMN);

        $memberStmt = $pdo->query("SELECT id FROM members LIMIT 2");
        $members = $memberStmt->fetchAll(PDO::FETCH_COLUMN);

        if (!empty($opportunities) && !empty($members)) {
            $sampleApplications = [
                [
                    'opportunity_id' => $opportunities[0],
                    'member_id' => $members[0],
                    'message' => 'I would love to help with this volunteer opportunity. I have experience in this area and am available on weekends.',
                    'status' => 'pending'
                ]
            ];

            if (count($opportunities) > 1 && count($members) > 1) {
                $sampleApplications[] = [
                    'opportunity_id' => $opportunities[1],
                    'member_id' => $members[1],
                    'message' => 'I am very interested in volunteering for this position. I have relevant skills and flexible schedule.',
                    'status' => 'approved'
                ];
            }

            $appStmt = $pdo->prepare("
                INSERT INTO volunteer_applications (opportunity_id, member_id, message, status, applied_date)
                VALUES (?, ?, ?, ?, NOW())
            ");

            foreach ($sampleApplications as $app) {
                try {
                    $appStmt->execute([
                        $app['opportunity_id'],
                        $app['member_id'],
                        $app['message'],
                        $app['status']
                    ]);
                } catch (PDOException $e) {
                    // Ignore duplicate key errors
                    if ($e->getCode() != 23000) {
                        echo "<p style='color: orange;'>⚠️ Could not add sample application: " . $e->getMessage() . "</p>";
                    }
                }
            }

            echo "<p style='color: green;'>✅ Added sample volunteer applications!</p>";
        }

    } else {
        echo "<p style='color: blue;'>ℹ️ volunteer_applications table already exists</p>";

        // Check if table has the required columns
        $stmt = $pdo->query("DESCRIBE volunteer_applications");
        $existingColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

        $requiredColumns = [
            'applied_date' => "ADD COLUMN `applied_date` timestamp NOT NULL DEFAULT current_timestamp()"
        ];

        foreach ($requiredColumns as $column => $sql) {
            if (!in_array($column, $existingColumns)) {
                try {
                    $pdo->exec("ALTER TABLE volunteer_applications $sql");
                    echo "<p style='color: green;'>✅ Added column: $column to volunteer_applications</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ Error adding column $column: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    // Add some sample data if tables are empty
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM volunteer_opportunities");
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        echo "<p>Adding sample volunteer opportunities...</p>";
        
        $sampleOpportunities = [
            [
                'title' => 'Welcome Team Volunteer',
                'description' => 'Help welcome guests and new members to our services. Provide information, assist with seating, and create a warm, friendly atmosphere.',
                'category' => 'hospitality',
                'contact_person' => 'Ndivhuwo Machiba',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+27686814477',
                'schedule_info' => 'Sundays 9:00 AM - 12:00 PM',
                'requirements' => 'Friendly personality, good communication skills'
            ],
            [
                'title' => 'Children\'s Ministry Helper',
                'description' => 'Assist with children\'s programs, help with activities, supervision, and creating a safe, fun environment for kids.',
                'category' => 'children',
                'contact_person' => 'Godwin Bointa',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+27686814477',
                'schedule_info' => 'Sundays 10:00 AM - 11:30 AM',
                'requirements' => 'Background check required, experience with children preferred'
            ],
            [
                'title' => 'Audio/Visual Team',
                'description' => 'Operate sound equipment, manage live streaming, and ensure quality audio/visual experience for services and events.',
                'category' => 'media',
                'contact_person' => 'Godwin Bointa',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+27686814477',
                'schedule_info' => 'Sundays 8:30 AM - 12:30 PM',
                'requirements' => 'Technical aptitude, willingness to learn equipment'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO volunteer_opportunities 
            (title, description, category, contact_person, contact_email, contact_phone, schedule_info, requirements, status, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)
        ");
        
        foreach ($sampleOpportunities as $opp) {
            $stmt->execute([
                $opp['title'],
                $opp['description'], 
                $opp['category'],
                $opp['contact_person'],
                $opp['contact_email'],
                $opp['contact_phone'],
                $opp['schedule_info'],
                $opp['requirements'],
                $_SESSION['admin_id']
            ]);
        }
        
        echo "<p style='color: green;'>✅ Added " . count($sampleOpportunities) . " sample volunteer opportunities!</p>";
    }
    
    echo "<h3 style='color: green;'>✅ All volunteer tables are now properly set up!</h3>";
    echo "<p><a href='volunteer_opportunities.php' class='btn btn-primary'>Go to Volunteer Opportunities</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
