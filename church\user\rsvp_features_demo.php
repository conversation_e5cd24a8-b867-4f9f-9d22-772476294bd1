<?php
session_start();
require_once '../config.php';

// Check if user is authenticated
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userData) {
        header('Location: login.php');
        exit;
    }
} catch (PDOException $e) {
    $error = "Error loading user data: " . $e->getMessage();
    $userData = ['first_name' => 'User', 'full_name' => 'User'];
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced RSVP Features - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .demo-container {
            margin-top: 2rem;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto"></div>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container demo-container">
        <!-- Demo Header -->
        <div class="demo-header">
            <h1><i class="bi bi-calendar-plus"></i> Enhanced RSVP Features</h1>
            <p class="mb-0">Discover the comprehensive RSVP system with guest management, dietary restrictions, and more!</p>
        </div>

        <!-- Feature Showcase -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="feature-card text-center h-100">
                    <i class="bi bi-people-fill feature-icon"></i>
                    <h4>Guest Registration</h4>
                    <p class="text-muted">Bring family and friends! Register multiple guests with detailed information including:</p>
                    <ul class="text-start">
                        <li>Guest names and contact information</li>
                        <li>Age groups (Child, Teen, Adult, Senior)</li>
                        <li>Relationship to member</li>
                        <li>Emergency contact details</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="feature-card text-center h-100">
                    <i class="bi bi-heart-pulse-fill feature-icon"></i>
                    <h4>Dietary & Special Needs</h4>
                    <p class="text-muted">We care about everyone's needs! Specify:</p>
                    <ul class="text-start">
                        <li>Dietary restrictions and allergies</li>
                        <li>Accessibility requirements</li>
                        <li>Special accommodations needed</li>
                        <li>Medical considerations</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="feature-card text-center h-100">
                    <i class="bi bi-car-front-fill feature-icon"></i>
                    <h4>Transportation & Carpooling</h4>
                    <p class="text-muted">Make getting to events easier:</p>
                    <ul class="text-start">
                        <li>Request transportation assistance</li>
                        <li>Offer rides to other members</li>
                        <li>Specify vehicle capacity</li>
                        <li>Coordinate carpooling arrangements</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="feature-card text-center h-100">
                    <i class="bi bi-chat-square-heart-fill feature-icon"></i>
                    <h4>Detailed Communication</h4>
                    <p class="text-muted">Stay connected and informed:</p>
                    <ul class="text-start">
                        <li>Add personal notes and comments</li>
                        <li>Share additional information</li>
                        <li>Communicate special requests</li>
                        <li>Update preferences anytime</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Demo Actions -->
        <div class="feature-card text-center">
            <h3 class="mb-4">Try the Enhanced RSVP System</h3>
            <p class="text-muted mb-4">Experience all these features by RSVPing to an event with the enhanced system.</p>
            
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="events.php" class="btn demo-button">
                    <i class="bi bi-calendar-event"></i> View Events
                </a>
                <a href="enhanced_rsvp.php?event_id=1" class="btn demo-button">
                    <i class="bi bi-calendar-plus"></i> Try Enhanced RSVP
                </a>
                <a href="dashboard.php" class="btn btn-outline-secondary">
                    <i class="bi bi-house"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- How to Access -->
        <div class="feature-card">
            <h4><i class="bi bi-question-circle"></i> How to Access Enhanced RSVP</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>From Events Page:</h6>
                    <ol>
                        <li>Go to <strong>Dashboard → Events</strong></li>
                        <li>Find any event you want to attend</li>
                        <li>Click <strong>"RSVP + Guests"</strong> button</li>
                        <li>Fill out the comprehensive RSVP form</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>Features Available:</h6>
                    <ul>
                        <li>✅ Multiple guest registration</li>
                        <li>✅ Dietary restrictions tracking</li>
                        <li>✅ Special needs accommodation</li>
                        <li>✅ Transportation coordination</li>
                        <li>✅ Emergency contact information</li>
                        <li>✅ Personal notes and comments</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
