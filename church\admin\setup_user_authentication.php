<?php
/**
 * User Authentication Schema Setup
 * 
 * This script safely applies the user authentication database schema changes
 * to enable user dashboard functionality in the church management system.
 */

session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include configuration
require_once '../config.php';
require_once '../classes/SecurityManager.php';

// Initialize SecurityManager
$security = new SecurityManager($pdo);
$security->setSecurityHeaders();

$messages = [];
$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_schema'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $errors[] = "Invalid form submission. Please try again.";
    } else {
        try {
            // Read the SQL schema file
            $schemaFile = '../sql/user_authentication_schema.sql';
            
            if (!file_exists($schemaFile)) {
                throw new Exception("Schema file not found: $schemaFile");
            }
            
            $sql = file_get_contents($schemaFile);
            
            if ($sql === false) {
                throw new Exception("Failed to read schema file");
            }
            
            // Begin transaction
            $pdo->beginTransaction();
            
            // Split SQL into individual statements
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                }
            );
            
            $executedCount = 0;
            $skippedCount = 0;
            
            foreach ($statements as $statement) {
                // Skip comments and empty statements
                if (empty(trim($statement)) || preg_match('/^\s*--/', $statement)) {
                    continue;
                }
                
                try {
                    $pdo->exec($statement);
                    $executedCount++;
                    $messages[] = "✓ Executed: " . substr(trim($statement), 0, 100) . "...";
                } catch (PDOException $e) {
                    // Check if it's a "column already exists" or similar non-critical error
                    if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                        strpos($e->getMessage(), 'already exists') !== false ||
                        strpos($e->getMessage(), 'Table') !== false && strpos($e->getMessage(), 'already exists') !== false) {
                        $skippedCount++;
                        $messages[] = "⚠ Skipped (already exists): " . substr(trim($statement), 0, 100) . "...";
                    } else {
                        throw $e; // Re-throw if it's a critical error
                    }
                }
            }
            
            // Commit transaction
            $pdo->commit();
            
            $success = true;
            $messages[] = "<strong>Schema setup completed successfully!</strong>";
            $messages[] = "Executed: $executedCount statements";
            $messages[] = "Skipped: $skippedCount statements (already existed)";
            
            // Log the schema setup
            $security->logSecurityEvent('User authentication schema setup', [
                'admin_id' => $_SESSION['admin_id'],
                'executed_statements' => $executedCount,
                'skipped_statements' => $skippedCount
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }
            
            $errors[] = "Error setting up schema: " . $e->getMessage();
            
            // Log the error
            error_log("User authentication schema setup error: " . $e->getMessage());
        }
    }
}

// Check current schema status
$schemaStatus = [];
try {
    // Check if user authentication columns exist in members table
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'password', 'password_reset_token', 'temp_password', 'must_change_password',
        'last_login_at', 'email_verified', 'notification_preferences'
    ];
    
    foreach ($requiredColumns as $column) {
        $schemaStatus['members_' . $column] = in_array($column, $columns);
    }
    
    // Check if new tables exist
    $requiredTables = [
        'user_login_attempts', 'user_activity_logs', 'user_sessions', 
        'events', 'event_rsvps', 'birthday_templates', 
        'user_birthday_messages', 'user_preferences'
    ];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $schemaStatus['table_' . $table] = $stmt->rowCount() > 0;
    }
    
} catch (Exception $e) {
    $errors[] = "Error checking schema status: " . $e->getMessage();
}

$page_title = 'User Authentication Setup';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Church Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-check {
            font-family: monospace;
            font-size: 0.9em;
        }
        .status-ok { color: #28a745; }
        .status-missing { color: #dc3545; }
        .log-messages {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0"><?php echo $page_title; ?></h1>
                        <p class="text-muted">Set up database schema for user authentication and dashboard functionality</p>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="bi bi-exclamation-triangle"></i> Errors</h5>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle"></i> Success</h5>
                        <p class="mb-0">User authentication schema has been set up successfully!</p>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-database"></i> Schema Setup
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>This will enhance your existing database with user authentication capabilities including:</p>
                                <ul>
                                    <li>User login with email or phone number</li>
                                    <li>Password management and reset functionality</li>
                                    <li>User activity tracking and audit logs</li>
                                    <li>Event management and RSVP system</li>
                                    <li>Birthday template system for users</li>
                                    <li>User preferences and settings</li>
                                </ul>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Safe Operation:</strong> This script will only add new columns and tables. 
                                    It will not modify or delete any existing data.
                                </div>

                                <form method="POST" class="mt-4">
                                    <?php echo $security->generateCSRFInput(); ?>
                                    <button type="submit" name="setup_schema" class="btn btn-primary">
                                        <i class="bi bi-play-circle"></i> Set Up User Authentication Schema
                                    </button>
                                </form>
                            </div>
                        </div>

                        <?php if (!empty($messages)): ?>
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-terminal"></i> Execution Log
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="log-messages">
                                        <?php foreach ($messages as $message): ?>
                                            <div><?php echo $message; ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-list-check"></i> Schema Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6>Members Table Columns:</h6>
                                <div class="status-check">
                                    <?php
                                    $memberColumns = ['password', 'password_reset_token', 'temp_password', 'must_change_password', 'last_login_at', 'email_verified', 'notification_preferences'];
                                    foreach ($memberColumns as $column):
                                        $exists = isset($schemaStatus['members_' . $column]) && $schemaStatus['members_' . $column];
                                        $class = $exists ? 'status-ok' : 'status-missing';
                                        $icon = $exists ? '✓' : '✗';
                                    ?>
                                        <div class="<?php echo $class; ?>"><?php echo $icon; ?> <?php echo $column; ?></div>
                                    <?php endforeach; ?>
                                </div>

                                <h6 class="mt-3">New Tables:</h6>
                                <div class="status-check">
                                    <?php
                                    $tables = ['user_login_attempts', 'user_activity_logs', 'user_sessions', 'events', 'event_rsvps', 'birthday_templates', 'user_birthday_messages', 'user_preferences'];
                                    foreach ($tables as $table):
                                        $exists = isset($schemaStatus['table_' . $table]) && $schemaStatus['table_' . $table];
                                        $class = $exists ? 'status-ok' : 'status-missing';
                                        $icon = $exists ? '✓' : '✗';
                                    ?>
                                        <div class="<?php echo $class; ?>"><?php echo $icon; ?> <?php echo $table; ?></div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
