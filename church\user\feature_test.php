<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Test - New Features</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4"><i class="bi bi-check-circle-fill text-success"></i> New Features Test Page</h1>
        
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle"></i> Testing New Features</h5>
            <p>This page helps you test the newly implemented features. Click the links below to access each feature:</p>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-person-gear text-primary"></i> Enhanced Profile Management</h5>
                        <p class="card-text">Comprehensive profile management with photo uploads, bio, social media, privacy settings, and more.</p>
                        <a href="profile.php" class="btn btn-primary">
                            <i class="bi bi-person"></i> Go to Profile
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-people text-info"></i> Family Management</h5>
                        <p class="card-text">Link family members, set permissions for profile management and event RSVPs.</p>
                        <a href="family_management.php" class="btn btn-info">
                            <i class="bi bi-people"></i> Manage Family
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-heart text-danger"></i> Prayer Requests</h5>
                        <p class="card-text">Create personal prayer requests, share with community, and pray for others.</p>
                        <a href="prayer_requests.php" class="btn btn-danger">
                            <i class="bi bi-heart"></i> Prayer Requests
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-calendar-event text-success"></i> Enhanced Events</h5>
                        <p class="card-text">Multi-session events, enhanced RSVP with guest registration, and carpooling coordination.</p>
                        <a href="events.php" class="btn btn-success">
                            <i class="bi bi-calendar-event"></i> View Events
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="bi bi-list-check"></i> Feature Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ Completed Features:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle-fill text-success"></i> Enhanced Profile Management</li>
                            <li><i class="bi bi-check-circle-fill text-success"></i> Family Member Linking</li>
                            <li><i class="bi bi-check-circle-fill text-success"></i> Prayer Request System</li>
                            <li><i class="bi bi-check-circle-fill text-success"></i> Multi-Session Events</li>
                            <li><i class="bi bi-check-circle-fill text-success"></i> Enhanced RSVP System</li>
                            <li><i class="bi bi-check-circle-fill text-success"></i> Carpooling Coordination</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>⏳ Coming Soon:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-clock text-warning"></i> Skills & Volunteer Matching</li>
                            <li><i class="bi bi-clock text-warning"></i> Birthday & Anniversary Celebrations</li>
                            <li><i class="bi bi-clock text-warning"></i> Automated Reminder System</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-lightbulb"></i> How to Test:</h6>
            <ol>
                <li><strong>Profile Management:</strong> Click "Go to Profile" to see the new tabbed interface with extended fields</li>
                <li><strong>Family Management:</strong> Click "Manage Family" to add family members and set permissions</li>
                <li><strong>Prayer Requests:</strong> Click "Prayer Requests" to create and manage prayer requests</li>
                <li><strong>Enhanced Events:</strong> Go to events to see multi-session support and enhanced RSVP</li>
            </ol>
        </div>

        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="bi bi-house"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
