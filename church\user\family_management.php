<?php
/**
 * Family Member Linking and Management System
 * Allows users to link family members and manage family groups
 */

session_start();
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_family_member'])) {
            // Add family member relationship
            $stmt = $pdo->prepare("
                INSERT INTO family_relationships 
                (member_id, related_member_id, relationship_type, can_manage_profile, can_rsvp_for_member)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $_POST['related_member_id'],
                $_POST['relationship_type'],
                isset($_POST['can_manage_profile']) ? 1 : 0,
                isset($_POST['can_rsvp_for_member']) ? 1 : 0
            ]);
            
            // Add reciprocal relationship
            $reciprocal_relationship = [
                'spouse' => 'spouse',
                'parent' => 'child',
                'child' => 'parent',
                'sibling' => 'sibling',
                'grandparent' => 'grandchild',
                'grandchild' => 'grandparent',
                'other' => 'other'
            ];
            
            $reciprocal = $reciprocal_relationship[$_POST['relationship_type']] ?? 'other';
            
            $stmt = $pdo->prepare("
                INSERT INTO family_relationships 
                (member_id, related_member_id, relationship_type, can_manage_profile, can_rsvp_for_member)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['related_member_id'],
                $userId,
                $reciprocal,
                0, // Default to no permissions for reciprocal relationship
                0
            ]);
            
            $message = "Family member added successfully!";
        }
        
        if (isset($_POST['update_permissions'])) {
            // Update family member permissions
            $stmt = $pdo->prepare("
                UPDATE family_relationships 
                SET can_manage_profile = ?, can_rsvp_for_member = ?
                WHERE id = ? AND member_id = ?
            ");
            
            $stmt->execute([
                isset($_POST['can_manage_profile']) ? 1 : 0,
                isset($_POST['can_rsvp_for_member']) ? 1 : 0,
                $_POST['relationship_id'],
                $userId
            ]);
            
            $message = "Permissions updated successfully!";
        }
        
        if (isset($_POST['remove_family_member'])) {
            // Remove family relationship (both directions)
            $stmt = $pdo->prepare("SELECT related_member_id FROM family_relationships WHERE id = ? AND member_id = ?");
            $stmt->execute([$_POST['relationship_id'], $userId]);
            $related_member = $stmt->fetch();
            
            if ($related_member) {
                // Remove both relationships
                $stmt = $pdo->prepare("DELETE FROM family_relationships WHERE (member_id = ? AND related_member_id = ?) OR (member_id = ? AND related_member_id = ?)");
                $stmt->execute([$userId, $related_member['related_member_id'], $related_member['related_member_id'], $userId]);
                
                $message = "Family member removed successfully!";
            }
        }
        
    } catch (PDOException $e) {
        $error = "Error managing family: " . $e->getMessage();
    }
}

// Get user's family members
try {
    // First check if family_relationships table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'family_relationships'");
    $table_exists = $stmt->fetch();

    if (!$table_exists) {
        // Create the table if it doesn't exist
        $sql = "CREATE TABLE family_relationships (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            related_member_id INT NOT NULL,
            relationship_type VARCHAR(50) NOT NULL,
            can_manage_profile BOOLEAN DEFAULT FALSE,
            can_rsvp_for_member BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_related_member_id (related_member_id),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (related_member_id) REFERENCES members(id) ON DELETE CASCADE,
            UNIQUE KEY unique_relationship (member_id, related_member_id)
        )";
        $pdo->exec($sql);
    }

    $stmt = $pdo->prepare("
        SELECT fr.*, m.full_name, m.email, m.phone_number, m.birth_date,
               COALESCE(m.profile_photo_path, m.image_path) as profile_photo_path
        FROM family_relationships fr
        JOIN members m ON fr.related_member_id = m.id
        WHERE fr.member_id = ?
        ORDER BY fr.relationship_type, m.full_name
    ");
    $stmt->execute([$userId]);
    $family_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $family_members = [];
    $error = "Error loading family members: " . $e->getMessage();
}

// Get all members for adding new family relationships (excluding current user and existing family)
try {
    $existing_family_ids = array_column($family_members, 'related_member_id');
    $existing_family_ids[] = $userId; // Exclude self
    
    $placeholders = str_repeat('?,', count($existing_family_ids) - 1) . '?';
    $stmt = $pdo->prepare("
        SELECT id, full_name, email 
        FROM members 
        WHERE id NOT IN ($placeholders) AND is_active = 1
        ORDER BY full_name
    ");
    $stmt->execute($existing_family_ids);
    $available_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $available_members = [];
}

// Get user data
$userData = $userAuth->getUserById($userId);

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Management - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <?php include 'includes/theme_css.php'; ?>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }
        
        .family-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.2s ease;
        }
        
        .family-card:hover {
            transform: translateY(-2px);
        }
        
        .family-member-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .family-member-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .relationship-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .permission-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto"></div>
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="family_management.php"><i class="bi bi-people"></i> Family</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-people-fill"></i> Family Management</h2>
                <p class="text-muted">Manage your family members and their permissions</p>
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFamilyModal">
                <i class="bi bi-person-plus"></i> Add Family Member
            </button>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Family Members -->
        <?php if (empty($family_members)): ?>
            <div class="family-card text-center">
                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">No Family Members Added</h4>
                <p class="text-muted">Start by adding your family members to manage their profiles and RSVP for events.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFamilyModal">
                    <i class="bi bi-person-plus"></i> Add Your First Family Member
                </button>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($family_members as $member): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="family-card">
                            <div class="d-flex align-items-center mb-3">
                                <?php if ($member['profile_photo_path']): ?>
                                    <img src="../<?php echo htmlspecialchars($member['profile_photo_path']); ?>" 
                                         class="family-member-avatar me-3" alt="Profile">
                                <?php else: ?>
                                    <div class="family-member-placeholder me-3">
                                        <i class="bi bi-person-fill text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($member['full_name']); ?></h6>
                                    <span class="badge bg-primary relationship-badge">
                                        <?php echo ucfirst($member['relationship_type']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <?php if ($member['email']): ?>
                                    <small class="text-muted d-block">
                                        <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($member['email']); ?>
                                    </small>
                                <?php endif; ?>
                                <?php if ($member['phone_number']): ?>
                                    <small class="text-muted d-block">
                                        <i class="bi bi-phone"></i> <?php echo htmlspecialchars($member['phone_number']); ?>
                                    </small>
                                <?php endif; ?>
                                <?php if ($member['birth_date']): ?>
                                    <small class="text-muted d-block">
                                        <i class="bi bi-calendar"></i> Born <?php echo date('M j, Y', strtotime($member['birth_date'])); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex gap-1 flex-wrap">
                                    <?php if ($member['can_manage_profile']): ?>
                                        <span class="badge bg-success permission-badge">Can Manage Profile</span>
                                    <?php endif; ?>
                                    <?php if ($member['can_rsvp_for_member']): ?>
                                        <span class="badge bg-info permission-badge">Can RSVP</span>
                                    <?php endif; ?>
                                    <?php if (!$member['can_manage_profile'] && !$member['can_rsvp_for_member']): ?>
                                        <span class="badge bg-secondary permission-badge">View Only</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editPermissions(<?php echo $member['id']; ?>, '<?php echo htmlspecialchars($member['full_name']); ?>', <?php echo $member['can_manage_profile']; ?>, <?php echo $member['can_rsvp_for_member']; ?>)">
                                    <i class="bi bi-gear"></i> Permissions
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="removeFamilyMember(<?php echo $member['id']; ?>, '<?php echo htmlspecialchars($member['full_name']); ?>')">
                                    <i class="bi bi-trash"></i> Remove
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Add Family Member Modal -->
    <div class="modal fade" id="addFamilyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Family Member</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php if (empty($available_members)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> All members are already linked to your family or no other members are available.
                            </div>
                        <?php else: ?>
                            <div class="mb-3">
                                <label class="form-label">Select Member *</label>
                                <select class="form-select" name="related_member_id" required>
                                    <option value="">Choose a member...</option>
                                    <?php foreach ($available_members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?>
                                            (<?php echo htmlspecialchars($member['email']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Relationship *</label>
                                <select class="form-select" name="relationship_type" required>
                                    <option value="">Select relationship...</option>
                                    <option value="spouse">Spouse</option>
                                    <option value="child">Child</option>
                                    <option value="parent">Parent</option>
                                    <option value="sibling">Sibling</option>
                                    <option value="grandparent">Grandparent</option>
                                    <option value="grandchild">Grandchild</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="can_manage_profile" id="can_manage_profile">
                                    <label class="form-check-label" for="can_manage_profile">
                                        Can manage their profile
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="can_rsvp_for_member" id="can_rsvp_for_member">
                                    <label class="form-check-label" for="can_rsvp_for_member">
                                        Can RSVP for them to events
                                    </label>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <?php if (!empty($available_members)): ?>
                            <button type="submit" name="add_family_member" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i> Add Family Member
                            </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Permissions Modal -->
    <div class="modal fade" id="editPermissionsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Permissions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="relationship_id" id="edit_relationship_id">

                        <p>Update permissions for <strong id="edit_member_name"></strong>:</p>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="can_manage_profile" id="edit_can_manage_profile">
                            <label class="form-check-label" for="edit_can_manage_profile">
                                Can manage their profile
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="can_rsvp_for_member" id="edit_can_rsvp_for_member">
                            <label class="form-check-label" for="edit_can_rsvp_for_member">
                                Can RSVP for them to events
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_permissions" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Permissions
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Remove Family Member Modal -->
    <div class="modal fade" id="removeFamilyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Remove Family Member</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="relationship_id" id="remove_relationship_id">

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            Are you sure you want to remove <strong id="remove_member_name"></strong> from your family?
                        </div>

                        <p>This will remove the family relationship in both directions and revoke all permissions.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="remove_family_member" class="btn btn-danger">
                            <i class="bi bi-trash"></i> Remove Family Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editPermissions(relationshipId, memberName, canManageProfile, canRsvp) {
            document.getElementById('edit_relationship_id').value = relationshipId;
            document.getElementById('edit_member_name').textContent = memberName;
            document.getElementById('edit_can_manage_profile').checked = canManageProfile == 1;
            document.getElementById('edit_can_rsvp_for_member').checked = canRsvp == 1;

            new bootstrap.Modal(document.getElementById('editPermissionsModal')).show();
        }

        function removeFamilyMember(relationshipId, memberName) {
            document.getElementById('remove_relationship_id').value = relationshipId;
            document.getElementById('remove_member_name').textContent = memberName;

            new bootstrap.Modal(document.getElementById('removeFamilyModal')).show();
        }

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
