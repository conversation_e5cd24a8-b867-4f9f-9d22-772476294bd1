<?php
/**
 * Advanced Features Database Migration - Phase 3
 * Final phase: Volunteer opportunities, member enhancements, and default data
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$migration_log = [];
$errors = [];

function log_migration($message, $type = 'info') {
    global $migration_log;
    $migration_log[] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
}

function execute_sql($pdo, $sql, $description) {
    global $errors;
    try {
        $pdo->exec($sql);
        log_migration("✓ $description", 'success');
        return true;
    } catch (PDOException $e) {
        $error_msg = "✗ $description - Error: " . $e->getMessage();
        log_migration($error_msg, 'error');
        $errors[] = $error_msg;
        return false;
    }
}

function column_exists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function table_exists($pdo, $table) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    log_migration("Starting Advanced Features Database Migration - Phase 3", 'info');
    
    try {
        $pdo->beginTransaction();
        
        // ============================================================================
        // VOLUNTEER OPPORTUNITIES & APPLICATIONS
        // ============================================================================
        
        if (!table_exists($pdo, 'volunteer_opportunities')) {
            $sql = "CREATE TABLE `volunteer_opportunities` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `title` varchar(255) NOT NULL,
              `description` text NOT NULL,
              `category` varchar(100) NOT NULL,
              `required_skills` text DEFAULT NULL COMMENT 'JSON array of required skill IDs',
              `preferred_skills` text DEFAULT NULL COMMENT 'JSON array of preferred skill IDs',
              `time_commitment` varchar(255) DEFAULT NULL,
              `schedule_type` enum('one_time','recurring','flexible','ongoing') NOT NULL DEFAULT 'one_time',
              `start_date` date DEFAULT NULL,
              `end_date` date DEFAULT NULL,
              `location` varchar(255) DEFAULT NULL,
              `contact_person_id` int(11) NOT NULL,
              `max_volunteers` int(11) DEFAULT NULL,
              `min_age` int(11) DEFAULT NULL,
              `background_check_required` tinyint(1) NOT NULL DEFAULT 0,
              `training_required` tinyint(1) NOT NULL DEFAULT 0,
              `training_description` text DEFAULT NULL,
              `status` enum('draft','active','filled','completed','cancelled') NOT NULL DEFAULT 'draft',
              `created_by` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_category` (`category`),
              KEY `idx_status` (`status`),
              KEY `idx_start_date` (`start_date`),
              KEY `idx_contact_person` (`contact_person_id`),
              FOREIGN KEY (`contact_person_id`) REFERENCES `members` (`id`) ON DELETE RESTRICT,
              FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created volunteer_opportunities table");
        }
        
        if (!table_exists($pdo, 'volunteer_applications')) {
            $sql = "CREATE TABLE `volunteer_applications` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `opportunity_id` int(11) NOT NULL,
              `member_id` int(11) NOT NULL,
              `application_message` text DEFAULT NULL,
              `availability` text DEFAULT NULL,
              `relevant_experience` text DEFAULT NULL,
              `status` enum('pending','approved','rejected','withdrawn') NOT NULL DEFAULT 'pending',
              `reviewed_by` int(11) DEFAULT NULL,
              `reviewed_at` datetime DEFAULT NULL,
              `review_notes` text DEFAULT NULL,
              `start_date` date DEFAULT NULL,
              `end_date` date DEFAULT NULL,
              `hours_committed` decimal(5,2) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_opportunity_member` (`opportunity_id`, `member_id`),
              KEY `idx_opportunity_id` (`opportunity_id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_status` (`status`),
              FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created volunteer_applications table");
        }
        
        if (!table_exists($pdo, 'volunteer_hours')) {
            $sql = "CREATE TABLE `volunteer_hours` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `opportunity_id` int(11) DEFAULT NULL,
              `date_served` date NOT NULL,
              `hours_served` decimal(5,2) NOT NULL,
              `description` text DEFAULT NULL,
              `verified_by` int(11) DEFAULT NULL,
              `verified_at` datetime DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_opportunity_id` (`opportunity_id`),
              KEY `idx_date_served` (`date_served`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE SET NULL,
              FOREIGN KEY (`verified_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created volunteer_hours table");
        }
        
        // ============================================================================
        // MEMBER PORTAL ENHANCEMENTS
        // ============================================================================
        
        if (!table_exists($pdo, 'member_achievements')) {
            $sql = "CREATE TABLE `member_achievements` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `achievement_type` enum('volunteer_hours','event_attendance','anniversary','birthday','skill_milestone','other') NOT NULL,
              `title` varchar(255) NOT NULL,
              `description` text DEFAULT NULL,
              `achievement_date` date NOT NULL,
              `points_awarded` int(11) DEFAULT 0,
              `badge_icon` varchar(255) DEFAULT NULL,
              `is_public` tinyint(1) NOT NULL DEFAULT 1,
              `created_by` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_achievement_type` (`achievement_type`),
              KEY `idx_achievement_date` (`achievement_date`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created member_achievements table");
        }
        
        if (!table_exists($pdo, 'celebration_events')) {
            $sql = "CREATE TABLE `celebration_events` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `celebration_type` enum('birthday','anniversary','baptism','membership','other') NOT NULL,
              `celebration_date` date NOT NULL,
              `title` varchar(255) NOT NULL,
              `description` text DEFAULT NULL,
              `is_public` tinyint(1) NOT NULL DEFAULT 1,
              `allow_wishes` tinyint(1) NOT NULL DEFAULT 1,
              `notify_community` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_celebration_type` (`celebration_type`),
              KEY `idx_celebration_date` (`celebration_date`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created celebration_events table");
        }
        
        if (!table_exists($pdo, 'celebration_wishes')) {
            $sql = "CREATE TABLE `celebration_wishes` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `celebration_id` int(11) NOT NULL,
              `sender_member_id` int(11) NOT NULL,
              `message` text NOT NULL,
              `is_public` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_celebration_id` (`celebration_id`),
              KEY `idx_sender_member_id` (`sender_member_id`),
              FOREIGN KEY (`celebration_id`) REFERENCES `celebration_events` (`id`) ON DELETE CASCADE,
              FOREIGN KEY (`sender_member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created celebration_wishes table");
        }
        
        if (!table_exists($pdo, 'member_activity_feed')) {
            $sql = "CREATE TABLE `member_activity_feed` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `member_id` int(11) NOT NULL,
              `activity_type` enum('event_rsvp','prayer_request','volunteer_signup','achievement','birthday','anniversary','profile_update','other') NOT NULL,
              `activity_title` varchar(255) NOT NULL,
              `activity_description` text DEFAULT NULL,
              `related_entity_type` varchar(50) DEFAULT NULL COMMENT 'events, prayer_requests, etc.',
              `related_entity_id` int(11) DEFAULT NULL,
              `is_public` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_member_id` (`member_id`),
              KEY `idx_activity_type` (`activity_type`),
              KEY `idx_created_at` (`created_at`),
              KEY `idx_related_entity` (`related_entity_type`, `related_entity_id`),
              FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            execute_sql($pdo, $sql, "Created member_activity_feed table");
        }
        
        // ============================================================================
        // ENHANCE MEMBERS TABLE
        // ============================================================================
        
        log_migration("Enhancing members table with new profile fields...", 'info');
        
        $member_columns = [
            'profile_photo_path' => "ADD COLUMN `profile_photo_path` varchar(255) DEFAULT NULL COMMENT 'High-resolution profile photo'",
            'cover_photo_path' => "ADD COLUMN `cover_photo_path` varchar(255) DEFAULT NULL COMMENT 'Cover/banner photo for profile'",
            'bio' => "ADD COLUMN `bio` text DEFAULT NULL COMMENT 'Personal biography'",
            'interests' => "ADD COLUMN `interests` text DEFAULT NULL COMMENT 'JSON array of interests/hobbies'",
            'social_media_links' => "ADD COLUMN `social_media_links` text DEFAULT NULL COMMENT 'JSON object of social media profiles'",
            'emergency_contact_name' => "ADD COLUMN `emergency_contact_name` varchar(255) DEFAULT NULL",
            'emergency_contact_phone' => "ADD COLUMN `emergency_contact_phone` varchar(20) DEFAULT NULL",
            'emergency_contact_relationship' => "ADD COLUMN `emergency_contact_relationship` varchar(100) DEFAULT NULL",
            'medical_conditions' => "ADD COLUMN `medical_conditions` text DEFAULT NULL COMMENT 'Important medical information'",
            'allergies' => "ADD COLUMN `allergies` text DEFAULT NULL COMMENT 'Known allergies'",
            'preferred_communication' => "ADD COLUMN `preferred_communication` enum('email','sms','phone','mail') DEFAULT 'email'",
            'privacy_settings' => "ADD COLUMN `privacy_settings` text DEFAULT NULL COMMENT 'JSON object of privacy preferences'",
            'notification_settings' => "ADD COLUMN `notification_settings` text DEFAULT NULL COMMENT 'JSON object of notification preferences'",
            'anniversary_date' => "ADD COLUMN `anniversary_date` date DEFAULT NULL COMMENT 'Wedding anniversary or membership anniversary'",
            'baptism_date' => "ADD COLUMN `baptism_date` date DEFAULT NULL",
            'membership_date' => "ADD COLUMN `membership_date` date DEFAULT NULL",
            'volunteer_interests' => "ADD COLUMN `volunteer_interests` text DEFAULT NULL COMMENT 'JSON array of volunteer interest categories'",
            'availability_schedule' => "ADD COLUMN `availability_schedule` text DEFAULT NULL COMMENT 'JSON object of general availability'"
        ];
        
        foreach ($member_columns as $column => $sql_part) {
            if (!column_exists($pdo, 'members', $column)) {
                execute_sql($pdo, "ALTER TABLE `members` $sql_part", "Added $column to members table");
            }
        }
        
        $pdo->commit();
        log_migration("Phase 3 migration completed successfully!", 'success');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        log_migration("Migration failed: " . $e->getMessage(), 'error');
        $errors[] = "Migration failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Features Migration - Phase 3</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-database-gear"></i> Advanced Features Migration - Phase 3 (Final)</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($migration_log)): ?>
                            <div class="alert alert-info">
                                <h5><i class="bi bi-info-circle"></i> Final Phase Migration</h5>
                                <p>This final phase will complete the advanced features setup:</p>
                                <ul>
                                    <li><strong>Volunteer Opportunities & Applications</strong> - Complete volunteer management system</li>
                                    <li><strong>Member Achievements</strong> - Recognition and milestone tracking</li>
                                    <li><strong>Celebration Events</strong> - Birthday and anniversary management</li>
                                    <li><strong>Enhanced Member Profiles</strong> - Extended profile fields and activity feeds</li>
                                    <li><strong>Default Data</strong> - Skills catalog and reminder templates</li>
                                </ul>
                                <p class="mb-0"><strong>Note:</strong> Ensure Phases 1 and 2 were completed successfully.</p>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" name="run_migration" class="btn btn-success btn-lg">
                                    <i class="bi bi-check-circle"></i> Complete Migration
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="migration-log">
                                <h5>Migration Log</h5>
                                <?php foreach ($migration_log as $entry): ?>
                                    <div class="alert alert-<?php echo $entry['type'] === 'error' ? 'danger' : ($entry['type'] === 'success' ? 'success' : 'info'); ?> py-2">
                                        <small class="text-muted"><?php echo $entry['timestamp']; ?></small><br>
                                        <?php echo htmlspecialchars($entry['message']); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($errors)): ?>
                                <div class="alert alert-success">
                                    <h5><i class="bi bi-check-circle"></i> All Migrations Completed Successfully! 🎉</h5>
                                    <p>Your organization management system now includes all advanced features:</p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Event Management</h6>
                                            <ul class="small">
                                                <li>Multi-session events</li>
                                                <li>Enhanced RSVP with guests</li>
                                                <li>Carpooling coordination</li>
                                                <li>Automated reminders</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Member Portal</h6>
                                            <ul class="small">
                                                <li>Family relationships</li>
                                                <li>Prayer requests</li>
                                                <li>Skills & volunteer matching</li>
                                                <li>Enhanced profiles & celebrations</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <a href="setup_default_data.php" class="btn btn-primary me-2">
                                            <i class="bi bi-gear"></i> Setup Default Data
                                        </a>
                                        <a href="dashboard.php" class="btn btn-success">
                                            <i class="bi bi-house"></i> Go to Dashboard
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5><i class="bi bi-exclamation-triangle"></i> Migration Completed with Errors</h5>
                                    <p>Some parts of the migration failed. Please review the log above.</p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
