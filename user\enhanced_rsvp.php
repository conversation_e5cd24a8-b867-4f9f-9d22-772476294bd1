<?php
/**
 * Enhanced RSVP System
 * Comprehensive RSVP with guest registration, dietary restrictions, and special needs
 */

session_start();
require_once '../church/config.php';
require_once '../church/classes/SecurityManager.php';
require_once '../church/classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Get existing RSVP if any
try {
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$event_id, $userId]);
    $existing_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $existing_rsvp = null;
}

// Get existing guests if any
$existing_guests = [];
if ($existing_rsvp) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM event_guests WHERE event_id = ? AND rsvp_id = ?");
        $stmt->execute([$event_id, $existing_rsvp['id']]);
        $existing_guests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $existing_guests = [];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_rsvp'])) {
    try {
        $pdo->beginTransaction();
        
        // Prepare RSVP data
        $rsvp_data = [
            'response' => $_POST['response'],
            'guests_count' => (int)($_POST['guests_count'] ?? 0),
            'notes' => $_POST['notes'] ?? '',
            'dietary_restrictions' => $_POST['dietary_restrictions'] ?? '',
            'special_needs' => $_POST['special_needs'] ?? '',
            'transportation_needed' => isset($_POST['transportation_needed']) ? 1 : 0,
            'can_provide_transportation' => isset($_POST['can_provide_transportation']) ? 1 : 0,
            'vehicle_capacity' => $_POST['vehicle_capacity'] ?? null,
            'emergency_contact_name' => $_POST['emergency_contact_name'] ?? '',
            'emergency_contact_phone' => $_POST['emergency_contact_phone'] ?? '',
            'additional_info' => $_POST['additional_info'] ?? ''
        ];
        
        if ($existing_rsvp) {
            // Update existing RSVP
            $stmt = $pdo->prepare("
                UPDATE event_rsvps SET 
                status = ?, guests_count = ?, notes = ?, dietary_restrictions = ?, 
                special_needs = ?, transportation_needed = ?, can_provide_transportation = ?, 
                vehicle_capacity = ?, emergency_contact_name = ?, emergency_contact_phone = ?, 
                additional_info = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([
                $rsvp_data['response'], $rsvp_data['guests_count'], $rsvp_data['notes'],
                $rsvp_data['dietary_restrictions'], $rsvp_data['special_needs'],
                $rsvp_data['transportation_needed'], $rsvp_data['can_provide_transportation'],
                $rsvp_data['vehicle_capacity'], $rsvp_data['emergency_contact_name'],
                $rsvp_data['emergency_contact_phone'], $rsvp_data['additional_info'],
                $existing_rsvp['id']
            ]);
            $rsvp_id = $existing_rsvp['id'];
            
            // Delete existing guests to re-add them
            $stmt = $pdo->prepare("DELETE FROM event_guests WHERE event_id = ? AND rsvp_id = ?");
            $stmt->execute([$event_id, $rsvp_id]);
        } else {
            // Create new RSVP
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps 
                (event_id, user_id, status, guests_count, notes, dietary_restrictions, 
                 special_needs, transportation_needed, can_provide_transportation, 
                 vehicle_capacity, emergency_contact_name, emergency_contact_phone, additional_info)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_id, $userId, $rsvp_data['response'], $rsvp_data['guests_count'],
                $rsvp_data['notes'], $rsvp_data['dietary_restrictions'], $rsvp_data['special_needs'],
                $rsvp_data['transportation_needed'], $rsvp_data['can_provide_transportation'],
                $rsvp_data['vehicle_capacity'], $rsvp_data['emergency_contact_name'],
                $rsvp_data['emergency_contact_phone'], $rsvp_data['additional_info']
            ]);
            $rsvp_id = $pdo->lastInsertId();
        }
        
        // Add guests if any
        if ($rsvp_data['guests_count'] > 0 && isset($_POST['guests'])) {
            $stmt = $pdo->prepare("
                INSERT INTO event_guests 
                (event_id, rsvp_id, guest_name, guest_email, guest_phone, age_group, 
                 relationship_to_member, dietary_restrictions, special_needs, 
                 emergency_contact_name, emergency_contact_phone)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($_POST['guests'] as $guest) {
                if (!empty($guest['name'])) {
                    $stmt->execute([
                        $event_id, $rsvp_id, $guest['name'], $guest['email'] ?? '',
                        $guest['phone'] ?? '', $guest['age_group'] ?? 'adult',
                        $guest['relationship'] ?? '', $guest['dietary_restrictions'] ?? '',
                        $guest['special_needs'] ?? '', $guest['emergency_contact_name'] ?? '',
                        $guest['emergency_contact_phone'] ?? ''
                    ]);
                }
            }
        }
        
        $pdo->commit();
        $message = "Your RSVP has been " . ($existing_rsvp ? "updated" : "submitted") . " successfully!";
        
        // Redirect to event detail page
        header("Location: event_detail.php?id=" . $event_id . "&rsvp_success=1");
        exit();
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        $error = "Error saving RSVP: " . $e->getMessage();
    }
}

// Get user data
$userData = $userAuth->getUserById($userId);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RSVP - <?php echo htmlspecialchars($event['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/user-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                <li class="breadcrumb-item"><a href="event_detail.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($event['title']); ?></a></li>
                <li class="breadcrumb-item active">RSVP</li>
            </ol>
        </nav>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Event Info -->
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="mb-2"><?php echo htmlspecialchars($event['title']); ?></h2>
                <p class="text-muted mb-1">
                    <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?>
                    <?php if ($event['location']): ?>
                        | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                    <?php endif; ?>
                </p>
                <?php if ($event['description']): ?>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- RSVP Form -->
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-calendar-check"></i> 
                    <?php echo $existing_rsvp ? 'Update Your RSVP' : 'RSVP to Event'; ?>
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="rsvpForm">
                    <!-- Response Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Will you be attending? *</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="attending" 
                                           id="response_attending" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'attending') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_attending">
                                        <i class="bi bi-check-circle text-success"></i> Yes, I'll be there
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="maybe" 
                                           id="response_maybe" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'maybe') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_maybe">
                                        <i class="bi bi-question-circle text-warning"></i> Maybe
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="not_attending" 
                                           id="response_not_attending" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'not_attending') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_not_attending">
                                        <i class="bi bi-x-circle text-danger"></i> No, I can't make it
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attending Options (shown only if attending/maybe) -->
                    <div id="attendingOptions" style="display: none;">
                        <!-- Guest Information -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Number of Guests</label>
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <input type="number" class="form-control" name="guests_count" id="guests_count" 
                                           min="0" max="10" value="<?php echo $existing_rsvp['guests_count'] ?? 0; ?>">
                                </div>
                                <div class="col-md-9">
                                    <small class="text-muted">How many additional people will you bring?</small>
                                </div>
                            </div>
                        </div>

                        <!-- Guest Details -->
                        <div id="guestDetails" style="display: none;">
                            <h5 class="mb-3">Guest Information</h5>
                            <div id="guestForms"></div>
                        </div>

                        <!-- Dietary Restrictions -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Dietary Restrictions & Allergies</label>
                            <textarea class="form-control" name="dietary_restrictions" rows="3" 
                                      placeholder="Please list any dietary restrictions, food allergies, or special meal requirements..."><?php echo htmlspecialchars($existing_rsvp['dietary_restrictions'] ?? ''); ?></textarea>
                        </div>

                        <!-- Special Needs -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Accessibility & Special Needs</label>
                            <textarea class="form-control" name="special_needs" rows="3" 
                                      placeholder="Please describe any accessibility needs, mobility assistance, or other accommodations required..."><?php echo htmlspecialchars($existing_rsvp['special_needs'] ?? ''); ?></textarea>
                        </div>

                        <!-- Transportation -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Transportation</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="transportation_needed" 
                                               id="transportation_needed" <?php echo ($existing_rsvp && $existing_rsvp['transportation_needed']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="transportation_needed">
                                            I need a ride to the event
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="can_provide_transportation" 
                                               id="can_provide_transportation" <?php echo ($existing_rsvp && $existing_rsvp['can_provide_transportation']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="can_provide_transportation">
                                            I can provide rides to others
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="vehicleCapacity" class="mt-2" style="display: none;">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">Vehicle Capacity</label>
                                        <input type="number" class="form-control" name="vehicle_capacity" 
                                               min="1" max="20" value="<?php echo $existing_rsvp['vehicle_capacity'] ?? ''; ?>"
                                               placeholder="How many people can you transport?">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Emergency Contact</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Contact Name</label>
                                    <input type="text" class="form-control" name="emergency_contact_name" 
                                           value="<?php echo htmlspecialchars($existing_rsvp['emergency_contact_name'] ?? ''); ?>"
                                           placeholder="Emergency contact person">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Contact Phone</label>
                                    <input type="tel" class="form-control" name="emergency_contact_phone" 
                                           value="<?php echo htmlspecialchars($existing_rsvp['emergency_contact_phone'] ?? ''); ?>"
                                           placeholder="Emergency contact phone number">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Additional Information</label>
                        <textarea class="form-control" name="additional_info" rows="3" 
                                  placeholder="Any other information, questions, or comments..."><?php echo htmlspecialchars($existing_rsvp['additional_info'] ?? ''); ?></textarea>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Notes</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="Any additional notes or comments..."><?php echo htmlspecialchars($existing_rsvp['notes'] ?? ''); ?></textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="event_detail.php?id=<?php echo $event_id; ?>" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Event
                        </a>
                        <button type="submit" name="submit_rsvp" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle"></i> 
                            <?php echo $existing_rsvp ? 'Update RSVP' : 'Submit RSVP'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const responseInputs = document.querySelectorAll('input[name="response"]');
            const attendingOptions = document.getElementById('attendingOptions');
            const guestsCountInput = document.getElementById('guests_count');
            const guestDetails = document.getElementById('guestDetails');
            const guestForms = document.getElementById('guestForms');
            const canProvideTransportation = document.getElementById('can_provide_transportation');
            const vehicleCapacity = document.getElementById('vehicleCapacity');

            // Show/hide attending options based on response
            function toggleAttendingOptions() {
                const selectedResponse = document.querySelector('input[name="response"]:checked');
                if (selectedResponse && (selectedResponse.value === 'attending' || selectedResponse.value === 'maybe')) {
                    attendingOptions.style.display = 'block';
                } else {
                    attendingOptions.style.display = 'none';
                }
            }

            // Show/hide vehicle capacity based on transportation checkbox
            function toggleVehicleCapacity() {
                if (canProvideTransportation.checked) {
                    vehicleCapacity.style.display = 'block';
                } else {
                    vehicleCapacity.style.display = 'none';
                }
            }

            // Generate guest forms based on count
            function generateGuestForms() {
                const count = parseInt(guestsCountInput.value) || 0;
                guestForms.innerHTML = '';
                
                if (count > 0) {
                    guestDetails.style.display = 'block';
                    
                    for (let i = 0; i < count; i++) {
                        const guestForm = createGuestForm(i + 1);
                        guestForms.appendChild(guestForm);
                    }
                } else {
                    guestDetails.style.display = 'none';
                }
            }

            // Create individual guest form
            function createGuestForm(guestNumber) {
                const div = document.createElement('div');
                div.className = 'card mb-3';
                div.innerHTML = `
                    <div class="card-header">
                        <h6 class="mb-0">Guest ${guestNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Name *</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][name]" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Age Group</label>
                                <select class="form-select" name="guests[${guestNumber-1}][age_group]">
                                    <option value="adult">Adult</option>
                                    <option value="teen">Teen (13-17)</option>
                                    <option value="child">Child (under 13)</option>
                                    <option value="senior">Senior (65+)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="guests[${guestNumber-1}][email]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-control" name="guests[${guestNumber-1}][phone]">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Relationship to You</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][relationship]" placeholder="e.g., spouse, child, friend">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Dietary Restrictions</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][dietary_restrictions]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Special Needs</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][special_needs]">
                            </div>
                        </div>
                    </div>
                `;
                return div;
            }

            // Event listeners
            responseInputs.forEach(input => {
                input.addEventListener('change', toggleAttendingOptions);
            });

            guestsCountInput.addEventListener('input', generateGuestForms);
            canProvideTransportation.addEventListener('change', toggleVehicleCapacity);

            // Initialize based on existing data
            toggleAttendingOptions();
            toggleVehicleCapacity();
            generateGuestForms();

            // Populate existing guest data if editing
            <?php if (!empty($existing_guests)): ?>
                // Set guest count
                guestsCountInput.value = <?php echo count($existing_guests); ?>;
                generateGuestForms();
                
                // Populate guest data
                <?php foreach ($existing_guests as $index => $guest): ?>
                    const guest<?php echo $index; ?>Form = guestForms.children[<?php echo $index; ?>];
                    if (guest<?php echo $index; ?>Form) {
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][name]"]').value = '<?php echo htmlspecialchars($guest['guest_name']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][email]"]').value = '<?php echo htmlspecialchars($guest['guest_email']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][phone]"]').value = '<?php echo htmlspecialchars($guest['guest_phone']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('select[name="guests[<?php echo $index; ?>][age_group]"]').value = '<?php echo $guest['age_group']; ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][relationship]"]').value = '<?php echo htmlspecialchars($guest['relationship_to_member']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][dietary_restrictions]"]').value = '<?php echo htmlspecialchars($guest['dietary_restrictions']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][special_needs]"]').value = '<?php echo htmlspecialchars($guest['special_needs']); ?>';
                    }
                <?php endforeach; ?>
            <?php endif; ?>
        });
    </script>
</body>
</html>
