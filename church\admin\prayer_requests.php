<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['update_status'])) {
            // Update prayer request status
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['status'],
                $_POST['prayer_request_id']
            ]);
            
            $message = "Prayer request status updated successfully!";
        }
        
        if (isset($_POST['delete_request'])) {
            // Delete prayer request (admin only)
            $stmt = $pdo->prepare("DELETE FROM prayer_requests WHERE id = ?");
            $stmt->execute([$_POST['prayer_request_id']]);
            
            $message = "Prayer request deleted successfully!";
        }
        
        if (isset($_POST['moderate_request'])) {
            // Moderate prayer request (hide/show)
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET privacy_level = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['new_privacy_level'],
                $_POST['prayer_request_id']
            ]);
            
            $message = "Prayer request moderated successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';
$privacy_filter = $_GET['privacy'] ?? 'all';

// Build query with filters
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "pr.status = ?";
    $params[] = $status_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "pr.category = ?";
    $params[] = $category_filter;
}

if ($privacy_filter !== 'all') {
    $where_conditions[] = "pr.privacy_level = ?";
    $params[] = $privacy_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get all prayer requests with member information
try {
    $stmt = $pdo->prepare("
        SELECT pr.*, m.full_name, m.email,
               COUNT(DISTINCT prr.id) as response_count
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        $where_clause
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
    ");
    $stmt->execute($params);
    $prayer_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $prayer_requests = [];
    $error = "Error loading prayer requests: " . $e->getMessage();
}

// Get statistics
try {
    $stats = [];
    
    // Total requests
    $stmt = $pdo->query("SELECT COUNT(*) FROM prayer_requests");
    $stats['total'] = $stmt->fetchColumn();
    
    // Active requests
    $stmt = $pdo->query("SELECT COUNT(*) FROM prayer_requests WHERE status = 'active'");
    $stats['active'] = $stmt->fetchColumn();
    
    // Answered requests
    $stmt = $pdo->query("SELECT COUNT(*) FROM prayer_requests WHERE status = 'answered'");
    $stats['answered'] = $stmt->fetchColumn();
    
    // Urgent requests
    $stmt = $pdo->query("SELECT COUNT(*) FROM prayer_requests WHERE is_urgent = 1 AND status = 'active'");
    $stats['urgent'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $stats = ['total' => 0, 'active' => 0, 'answered' => 0, 'urgent' => 0];
}

// Set page variables
$page_title = 'Prayer Requests Management';
$page_header = 'Prayer Requests Management';
$page_description = 'View and manage member prayer requests';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-heart"></i> Prayer Requests Management</h2>
                    <p class="text-muted">View and manage member prayer requests</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-heart fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['total']; ?></h3>
                                    <small>Total Requests</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-heart-pulse fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['active']; ?></h3>
                                    <small>Active Requests</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-heart-fill fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['answered']; ?></h3>
                                    <small>Answered Prayers</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-exclamation-triangle fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['urgent']; ?></h3>
                                    <small>Urgent Requests</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel"></i> Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" name="status" id="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="answered" <?php echo $status_filter === 'answered' ? 'selected' : ''; ?>>Answered</option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" name="category" id="category">
                                <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>All Categories</option>
                                <option value="personal" <?php echo $category_filter === 'personal' ? 'selected' : ''; ?>>Personal</option>
                                <option value="family" <?php echo $category_filter === 'family' ? 'selected' : ''; ?>>Family</option>
                                <option value="health" <?php echo $category_filter === 'health' ? 'selected' : ''; ?>>Health</option>
                                <option value="work" <?php echo $category_filter === 'work' ? 'selected' : ''; ?>>Work</option>
                                <option value="ministry" <?php echo $category_filter === 'ministry' ? 'selected' : ''; ?>>Ministry</option>
                                <option value="community" <?php echo $category_filter === 'community' ? 'selected' : ''; ?>>Community</option>
                                <option value="other" <?php echo $category_filter === 'other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="privacy" class="form-label">Privacy Level</label>
                            <select class="form-select" name="privacy" id="privacy">
                                <option value="all" <?php echo $privacy_filter === 'all' ? 'selected' : ''; ?>>All Privacy Levels</option>
                                <option value="private" <?php echo $privacy_filter === 'private' ? 'selected' : ''; ?>>Private</option>
                                <option value="family" <?php echo $privacy_filter === 'family' ? 'selected' : ''; ?>>Family</option>
                                <option value="members" <?php echo $privacy_filter === 'members' ? 'selected' : ''; ?>>Members</option>
                                <option value="public" <?php echo $privacy_filter === 'public' ? 'selected' : ''; ?>>Public</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                                <a href="prayer_requests.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Prayer Requests List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list"></i> Prayer Requests (<?php echo count($prayer_requests); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($prayer_requests)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-heart text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">No Prayer Requests Found</h4>
                            <p class="text-muted">No prayer requests match your current filters.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Privacy</th>
                                        <th>Status</th>
                                        <th>Responses</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($prayer_requests as $request): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($request['full_name']); ?></strong>
                                            <?php if ($request['is_anonymous']): ?>
                                                <br><small class="text-muted"><i class="bi bi-incognito"></i> Anonymous</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($request['title']); ?></strong>
                                            <?php if ($request['is_urgent']): ?>
                                                <span class="badge bg-danger ms-1">Urgent</span>
                                            <?php endif; ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($request['description'], 0, 100)); ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo ucfirst($request['category']); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $request['privacy_level'] === 'private' ? 'dark' :
                                                    ($request['privacy_level'] === 'family' ? 'warning' :
                                                    ($request['privacy_level'] === 'members' ? 'info' : 'success'));
                                            ?>">
                                                <?php echo ucfirst($request['privacy_level']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $request['status'] === 'active' ? 'success' :
                                                    ($request['status'] === 'answered' ? 'primary' : 'secondary');
                                            ?>">
                                                <?php echo ucfirst($request['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark"><?php echo $request['response_count']; ?> responses</span>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($request['created_at'])); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-success" onclick="updateStatus(<?php echo $request['id']; ?>, '<?php echo $request['status']; ?>')">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title']); ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus(requestId, currentStatus) {
    const newStatus = prompt(`Current status: ${currentStatus}\nEnter new status (active, answered, closed):`, currentStatus);
    if (newStatus && ['active', 'answered', 'closed'].includes(newStatus)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="prayer_request_id" value="${requestId}">
            <input type="hidden" name="status" value="${newStatus}">
            <input type="hidden" name="update_status" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteRequest(requestId, title) {
    if (confirm(`Are you sure you want to delete the prayer request "${title}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="prayer_request_id" value="${requestId}">
            <input type="hidden" name="delete_request" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
