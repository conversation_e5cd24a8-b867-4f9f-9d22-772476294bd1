<?php
/**
 * Setup Default Data for Advanced Features
 * Populates the database with default skills, reminder templates, and sample data
 */

session_start();
require_once '../church/config.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$setup_log = [];
$errors = [];

function log_setup($message, $type = 'info') {
    global $setup_log;
    $setup_log[] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
}

function execute_sql($pdo, $sql, $description) {
    global $errors;
    try {
        $pdo->exec($sql);
        log_setup("✓ $description", 'success');
        return true;
    } catch (PDOException $e) {
        $error_msg = "✗ $description - Error: " . $e->getMessage();
        log_setup($error_msg, 'error');
        $errors[] = $error_msg;
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_data'])) {
    log_setup("Setting up default data for advanced features...", 'info');
    
    try {
        $pdo->beginTransaction();
        
        // ============================================================================
        // DEFAULT SKILLS CATALOG
        // ============================================================================
        
        log_setup("Adding default skills to catalog...", 'info');
        
        $skills = [
            ['Public Speaking', 'Communication', 'Ability to speak confidently in front of groups'],
            ['Music - Vocals', 'Music & Arts', 'Singing and vocal performance'],
            ['Music - Piano', 'Music & Arts', 'Piano playing skills'],
            ['Music - Guitar', 'Music & Arts', 'Guitar playing skills'],
            ['Music - Drums', 'Music & Arts', 'Drum playing skills'],
            ['Teaching', 'Education', 'Ability to teach and educate others'],
            ['Childcare', 'Care & Support', 'Experience caring for children'],
            ['Youth Ministry', 'Care & Support', 'Working with teenagers and young adults'],
            ['Counseling', 'Care & Support', 'Providing emotional and spiritual support'],
            ['Event Planning', 'Organization', 'Planning and coordinating events'],
            ['Photography', 'Media & Technology', 'Photography skills for events and documentation'],
            ['Video Production', 'Media & Technology', 'Video recording and editing'],
            ['Audio/Sound', 'Media & Technology', 'Sound system operation and audio mixing'],
            ['Web Development', 'Technology', 'Website development and maintenance'],
            ['Graphic Design', 'Media & Technology', 'Creating visual designs and materials'],
            ['Social Media', 'Media & Technology', 'Managing social media accounts and content'],
            ['Cooking', 'Hospitality', 'Preparing meals for groups'],
            ['Hospitality', 'Hospitality', 'Welcoming and hosting guests'],
            ['Cleaning', 'Hospitality', 'Facility cleaning and maintenance'],
            ['Administrative', 'Organization', 'Office and administrative tasks'],
            ['Financial Management', 'Organization', 'Managing finances and budgets'],
            ['Data Entry', 'Organization', 'Computer data entry and record keeping'],
            ['Construction', 'Maintenance & Repair', 'Building and construction skills'],
            ['Electrical Work', 'Maintenance & Repair', 'Electrical repairs and installation'],
            ['Plumbing', 'Maintenance & Repair', 'Plumbing repairs and maintenance'],
            ['Gardening', 'Maintenance & Repair', 'Landscaping and garden maintenance'],
            ['Painting', 'Maintenance & Repair', 'Interior and exterior painting'],
            ['Transportation', 'Service', 'Providing rides and transportation'],
            ['Security', 'Service', 'Security and safety monitoring'],
            ['First Aid/CPR', 'Health & Safety', 'Emergency medical response'],
            ['Prayer Ministry', 'Spiritual', 'Leading prayer and spiritual support'],
            ['Bible Study', 'Spiritual', 'Leading Bible studies and small groups'],
            ['Worship Leading', 'Spiritual', 'Leading worship services'],
            ['Missions', 'Spiritual', 'Mission work and outreach'],
            ['Evangelism', 'Spiritual', 'Sharing faith and evangelistic outreach']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO skills_catalog (skill_name, skill_category, description, created_by) VALUES (?, ?, ?, ?)");
        $admin_id = $_SESSION['admin_id'];
        
        foreach ($skills as $skill) {
            $stmt->execute([$skill[0], $skill[1], $skill[2], $admin_id]);
        }
        log_setup("Added " . count($skills) . " default skills to catalog", 'success');
        
        // ============================================================================
        // DEFAULT REMINDER TEMPLATES
        // ============================================================================
        
        log_setup("Creating default reminder templates...", 'info');
        
        $templates = [
            [
                'Event Reminder - 7 Days',
                'General event reminder sent 7 days before',
                null,
                7,
                'email',
                'Reminder: {{event_title}} is coming up!',
                "Hi {{member_name}},\n\nThis is a friendly reminder that {{event_title}} is scheduled for {{event_date}} at {{event_location}}.\n\nWe look forward to seeing you there!\n\nBlessings,\n{{organization_name}}"
            ],
            [
                'Event Reminder - 1 Day',
                'Final reminder sent 1 day before event',
                null,
                1,
                'both',
                'Tomorrow: {{event_title}}',
                "Hi {{member_name}},\n\nJust a quick reminder that {{event_title}} is tomorrow ({{event_date}}) at {{event_location}}.\n\nSee you there!\n\n{{organization_name}}"
            ],
            [
                'Workshop Reminder - 3 Days',
                'Workshop-specific reminder',
                'workshop',
                3,
                'email',
                'Workshop Reminder: {{event_title}}',
                "Hi {{member_name}},\n\n{{event_title}} is coming up in 3 days!\n\nDate: {{event_date}}\nLocation: {{event_location}}\n\nPlease bring: {{materials_needed}}\n\nLooking forward to learning together!\n\n{{organization_name}}"
            ],
            [
                'Service Reminder - 2 Hours',
                'Last-minute service reminder',
                'service',
                0,
                'sms',
                'Service starts in 2 hours',
                "{{member_name}}, {{event_title}} starts in 2 hours at {{event_location}}. See you there!"
            ],
            [
                'Meeting Reminder - 1 Day',
                'Meeting reminder for committee meetings',
                'meeting',
                1,
                'email',
                'Meeting Tomorrow: {{event_title}}',
                "Hi {{member_name}},\n\nReminder: {{event_title}} is scheduled for tomorrow at {{event_time}}.\n\nLocation: {{event_location}}\n\nPlease review any materials sent previously.\n\nThank you,\n{{organization_name}}"
            ]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO reminder_templates (name, description, event_type, days_before_event, reminder_type, subject_template, message_template, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($templates as $template) {
            $stmt->execute([
                $template[0], $template[1], $template[2], $template[3],
                $template[4], $template[5], $template[6], $admin_id
            ]);
        }
        log_setup("Created " . count($templates) . " default reminder templates", 'success');
        
        // ============================================================================
        // CREATE SAMPLE VOLUNTEER OPPORTUNITIES
        // ============================================================================
        
        log_setup("Creating sample volunteer opportunities...", 'info');
        
        // Get a sample member ID for contact person
        $stmt = $pdo->query("SELECT id FROM members LIMIT 1");
        $sample_member = $stmt->fetch();
        
        if ($sample_member) {
            $opportunities = [
                [
                    'Welcome Team Volunteer',
                    'Help welcome guests and new members at our services. Provide information, assist with seating, and create a warm, friendly atmosphere.',
                    'Hospitality',
                    '["Hospitality", "Public Speaking"]',
                    '["Customer Service"]',
                    '2-3 hours per week',
                    'recurring',
                    date('Y-m-d'),
                    null,
                    'Main Sanctuary Entrance',
                    $sample_member['id'],
                    10,
                    16,
                    0,
                    1,
                    'Basic training on greeting procedures and facility layout'
                ],
                [
                    'Children\'s Ministry Helper',
                    'Assist with children\'s programs during services. Help with activities, supervision, and creating a safe, fun environment for kids.',
                    'Children & Youth',
                    '["Childcare", "Teaching"]',
                    '["First Aid/CPR", "Music"]',
                    '3-4 hours per week',
                    'recurring',
                    date('Y-m-d'),
                    null,
                    'Children\'s Wing',
                    $sample_member['id'],
                    8,
                    18,
                    1,
                    1,
                    'Background check and child safety training required'
                ],
                [
                    'Audio/Visual Team',
                    'Operate sound and video equipment during services and events. Ensure high-quality audio and visual presentation.',
                    'Media & Technology',
                    '["Audio/Sound", "Video Production"]',
                    '["Technical Skills"]',
                    '4-5 hours per week',
                    'recurring',
                    date('Y-m-d'),
                    null,
                    'Main Sanctuary',
                    $sample_member['id'],
                    5,
                    16,
                    0,
                    1,
                    'Training on equipment operation and troubleshooting'
                ]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO volunteer_opportunities (title, description, category, required_skills, preferred_skills, time_commitment, schedule_type, start_date, end_date, location, contact_person_id, max_volunteers, min_age, background_check_required, training_required, training_description, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)");
            
            foreach ($opportunities as $opp) {
                $stmt->execute([
                    $opp[0], $opp[1], $opp[2], $opp[3], $opp[4], $opp[5],
                    $opp[6], $opp[7], $opp[8], $opp[9], $opp[10], $opp[11],
                    $opp[12], $opp[13], $opp[14], $opp[15], $admin_id
                ]);
            }
            log_setup("Created " . count($opportunities) . " sample volunteer opportunities", 'success');
        }
        
        // ============================================================================
        // CREATE DATABASE VIEWS
        // ============================================================================
        
        log_setup("Creating database views for performance...", 'info');
        
        // Events with details view
        $view_sql = "CREATE OR REPLACE VIEW events_with_details AS
        SELECT 
            e.*,
            ec.name as category_name,
            ec.color_code as category_color,
            a.full_name as created_by_name,
            COUNT(DISTINCT er.id) as total_rsvps,
            COUNT(DISTINCT CASE WHEN er.response = 'yes' THEN er.id END) as yes_rsvps,
            COUNT(DISTINCT CASE WHEN er.response = 'no' THEN er.id END) as no_rsvps,
            COUNT(DISTINCT CASE WHEN er.response = 'maybe' THEN er.id END) as maybe_rsvps,
            COUNT(DISTINCT es.id) as session_count,
            COUNT(DISTINCT eg.id) as guest_count
        FROM events e
        LEFT JOIN event_categories ec ON e.category_id = ec.id
        LEFT JOIN admins a ON e.created_by = a.id
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        LEFT JOIN event_sessions es ON e.id = es.event_id
        LEFT JOIN event_guests eg ON e.id = eg.event_id
        GROUP BY e.id";
        execute_sql($pdo, $view_sql, "Created events_with_details view");
        
        // Member profiles summary view
        $view_sql = "CREATE OR REPLACE VIEW member_profiles_summary AS
        SELECT 
            m.*,
            COUNT(DISTINCT er.id) as total_event_rsvps,
            COUNT(DISTINCT va.id) as volunteer_applications,
            COUNT(DISTINCT vh.id) as volunteer_hours_logged,
            COUNT(DISTINCT pr.id) as prayer_requests_count,
            COUNT(DISTINCT ms.id) as skills_count,
            COUNT(DISTINCT fr.id) as family_relationships_count
        FROM members m
        LEFT JOIN event_rsvps er ON m.id = er.member_id
        LEFT JOIN volunteer_applications va ON m.id = va.member_id
        LEFT JOIN volunteer_hours vh ON m.id = vh.member_id
        LEFT JOIN prayer_requests pr ON m.id = pr.member_id
        LEFT JOIN member_skills ms ON m.id = ms.member_id
        LEFT JOIN family_relationships fr ON m.id = fr.primary_member_id OR m.id = fr.related_member_id
        GROUP BY m.id";
        execute_sql($pdo, $view_sql, "Created member_profiles_summary view");
        
        $pdo->commit();
        log_setup("Default data setup completed successfully!", 'success');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        log_setup("Setup failed: " . $e->getMessage(), 'error');
        $errors[] = "Setup failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Default Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-gear-fill"></i> Setup Default Data</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($setup_log)): ?>
                            <div class="alert alert-info">
                                <h5><i class="bi bi-info-circle"></i> Ready to Setup Default Data</h5>
                                <p>This will populate your system with useful default data:</p>
                                <ul>
                                    <li><strong>Skills Catalog</strong> - 35+ common skills across various categories</li>
                                    <li><strong>Reminder Templates</strong> - Pre-configured email/SMS reminder templates</li>
                                    <li><strong>Sample Volunteer Opportunities</strong> - Example volunteer positions</li>
                                    <li><strong>Database Views</strong> - Performance-optimized views for reporting</li>
                                </ul>
                                <p class="mb-0"><strong>Note:</strong> This is optional but recommended for getting started quickly.</p>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" name="setup_data" class="btn btn-primary btn-lg">
                                    <i class="bi bi-download"></i> Setup Default Data
                                </button>
                                <a href="dashboard.php" class="btn btn-secondary btn-lg ms-2">
                                    <i class="bi bi-skip-end"></i> Skip for Now
                                </a>
                            </form>
                        <?php else: ?>
                            <div class="setup-log">
                                <h5>Setup Log</h5>
                                <?php foreach ($setup_log as $entry): ?>
                                    <div class="alert alert-<?php echo $entry['type'] === 'error' ? 'danger' : ($entry['type'] === 'success' ? 'success' : 'info'); ?> py-2">
                                        <small class="text-muted"><?php echo $entry['timestamp']; ?></small><br>
                                        <?php echo htmlspecialchars($entry['message']); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($errors)): ?>
                                <div class="alert alert-success">
                                    <h5><i class="bi bi-check-circle"></i> Setup Completed Successfully! 🎉</h5>
                                    <p>Your system is now ready with all advanced features and default data. You can now:</p>
                                    <ul>
                                        <li>Create multi-session events and workshops</li>
                                        <li>Set up volunteer opportunities and match skills</li>
                                        <li>Enable prayer request tracking</li>
                                        <li>Configure family relationships</li>
                                        <li>Use automated reminder sequences</li>
                                    </ul>
                                    <div class="mt-3">
                                        <a href="dashboard.php" class="btn btn-success btn-lg">
                                            <i class="bi bi-house"></i> Go to Dashboard
                                        </a>
                                        <a href="../user/dashboard.php" class="btn btn-primary btn-lg ms-2">
                                            <i class="bi bi-person"></i> View Member Portal
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5><i class="bi bi-exclamation-triangle"></i> Setup Completed with Errors</h5>
                                    <p>Some parts of the setup failed. Please review the log above.</p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
