<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Redirect to dashboard.php
header("Location: dashboard.php");
exit();

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Handle test email
if (isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    $test_name = $_POST['test_name'] ?? 'Administrator';
    
    $test_subject = 'Campaign Management System - Email Test';
    $test_body = '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333;">Email Configuration Test</h2>
        <p style="line-height: 1.6; color: #555;">This is a test email from Campaign Management System admin panel. If you are receiving this email, it means your email configuration is working correctly.</p>
        <p style="line-height: 1.6; color: #555;">You can now send birthday messages and other emails to members.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            This email was sent from Campaign Management System Admin Panel.
        </div>
    </div>';
    
    global $last_email_error;
    if (sendEmail($test_email, $test_name, $test_subject, $test_body)) {
        $message = "Test email sent successfully to $test_email";
    } else {
        $error = "Failed to send test email. Error details: " . ($last_email_error ?? "Unknown error");
    }
}

// Get total member count
$totalMembers = 0;
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM members");
$stmt->execute();
$result = $stmt->fetch();
if ($result) {
    $totalMembers = $result['total'];
}

// Get birthday celebrants for the current month
$birthdays = [];
$currentMonth = date('m');
$stmt = $conn->prepare("SELECT id, full_name, birth_date, email FROM members WHERE MONTH(birth_date) = ?");
$stmt->execute([$currentMonth]);
$birthdays = $stmt->fetchAll();

// Get recent members
$recentMembers = [];
$recentMembersCount = 0;
$stmt = $conn->prepare("SELECT id, full_name, email, created_at, image_path FROM members ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recentMembers = $stmt->fetchAll();
$recentMembersCount = count($recentMembers);

// Close connection
$conn = null;

// Set page variables
$page_title = 'Dashboard';
$page_header = 'Dashboard';
$page_description = 'Welcome, ' . $_SESSION['admin_name'] . '!';

// Add extra CSS and JS for calendar
$extra_css = '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">';
$extra_js = '
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script src="js/calendar-debug.js"></script>
';

// Include header
include 'includes/header.php';
?>

<!-- Enhanced Stats Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-dashboard">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Organization Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Total Members -->
                    <div class="col-md-3 mb-4">
                        <div class="stat-card p-3 bg-light-blue rounded-3">
                            <div class="d-flex align-items-center">
                                <div class="icon-container bg-primary p-3 rounded-circle me-3">
                                    <i class="bi bi-people fs-3 text-white"></i>
                                </div>
                                <div>
                                    <h3 class="stat-value mb-0"><?= $totalMembers ?></h3>
                                    <span class="stat-label text-muted">Total Members</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Birthdays -->
                    <div class="col-md-3 mb-4">
                        <div class="stat-card p-3 bg-light-warning rounded-3">
                            <div class="d-flex align-items-center">
                                <div class="icon-container bg-warning p-3 rounded-circle me-3">
                                    <i class="bi bi-gift fs-3 text-dark"></i>
                                </div>
                                <div>
                                    <h3 class="stat-value mb-0"><?= count($birthdays) ?></h3>
                                    <span class="stat-label text-muted">This Month's Birthdays</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Templates -->
                    <div class="col-md-3 mb-4">
                        <?php
                        $templateCount = $pdo->query("SELECT COUNT(*) FROM email_templates")->fetchColumn();
                        ?>
                        <div class="stat-card p-3 bg-light-success rounded-3">
                            <div class="d-flex align-items-center">
                                <div class="icon-container bg-success p-3 rounded-circle me-3">
                                    <i class="bi bi-envelope fs-3 text-white"></i>
                                </div>
                                <div>
                                    <h3 class="stat-value mb-0"><?= $templateCount ?></h3>
                                    <span class="stat-label text-muted">Email Templates</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="col-md-3 mb-4">
                        <?php
                        $recentEmails = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE sent_at >= NOW() - INTERVAL 7 DAY")->fetchColumn();
                        ?>
                        <div class="stat-card p-3 bg-light-purple rounded-3">
                            <div class="d-flex align-items-center">
                                <div class="icon-container bg-purple p-3 rounded-circle me-3">
                                    <i class="bi bi-activity fs-3 text-white"></i>
                                </div>
                                <div>
                                    <h3 class="stat-value mb-0"><?= $recentEmails ?></h3>
                                    <span class="stat-label text-muted">Emails Sent (7 Days)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cron Job Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>Birthday Reminder Status
                        </h5>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-primary" id="checkCronStatus">
                            <i class="bi bi-arrow-clockwise me-1"></i>Check Status
                        </button>
                    </div>
                </div>
                <div id="cronStatusAlert" class="alert mt-3 d-none">
                    <!-- Status will be shown here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Make Calendar and Birthday sections equal height and width -->
<div class="row row-equal-height">
    <div class="col-md-6 calendar-section-col">
        <div class="card calendar-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Calendar</h5>
                <div>
                    <span class="badge bg-warning text-dark me-2">🎂 Birthdays</span>
                </div>
            </div>
            <div class="card-body">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 birthday-section-col">
        <div class="card birthday-card">
            <div class="card-header">
                <h5 class="mb-0">Birthday Celebrants (<?= date('F') ?>)</h5>
            </div>
            <div class="card-body">
                <div class="birthday-celebrants-list">
                    <?php if (count($birthdays) > 0): ?>
                        <ul class="list-group">
                            <?php foreach ($birthdays as $birthday): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-gift me-2 text-warning"></i>
                                        <span><?= $birthday['full_name'] ?></span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-dark me-2">
                                            <?php
                                            $date = new DateTime($birthday['birth_date']);
                                            echo $date->format('d M');
                                            ?>
                                        </span>
                                        <button class="btn btn-sm btn-outline-primary send-birthday-btn" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#sendBirthdayModal"
                                                data-id="<?= $birthday['id'] ?>"
                                                data-name="<?= $birthday['full_name'] ?>"
                                                data-email="<?= $birthday['email'] ?>">
                                            <i class="bi bi-envelope"></i>
                                        </button>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-info">
                            No birthdays this month.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Visualization Section -->
<div class="row data-visualization">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Analytics Dashboard</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <!-- First Row of Charts -->
                    <div class="col-md-6 mb-4">
                        <div class="chart-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Member Growth Trends</h6>
                                <div class="chart-legend-indicator">
                                    <span class="badge bg-primary me-2">New Members</span>
                                    <span class="badge bg-success">Attendance</span>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="memberGrowthChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-card">
                            <div class="card-header">
                                <h6 class="mb-0">Age Distribution</h6>
                            </div>
                            <div class="chart-container">
                                <canvas id="ageDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Second Row of Charts -->
                    <div class="col-md-4 mb-4">
                        <div class="chart-card">
                            <div class="card-header">
                                <h6 class="mb-0">Birthday Distribution</h6>
                            </div>
                            <div class="chart-container">
                                <canvas id="birthdayDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="chart-card">
                            <div class="card-header">
                                <h6 class="mb-0">Email Engagement</h6>
                            </div>
                            <div class="chart-container">
                                <canvas id="emailEngagementChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="chart-card">
                            <div class="card-header">
                                <h6 class="mb-0">Membership Status</h6>
                            </div>
                            <div class="chart-container">
                                <canvas id="membershipStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Begin Recent Members Section with enhanced styling -->
<div class="card recent-members-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Recent Members</h5>
        <span class="badge bg-primary rounded-pill"><?php echo $recentMembersCount; ?> New</span>
    </div>
    <div class="card-body p-0">
        <table class="table table-recent-members mb-0">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Joined</th>
                    <th class="text-center">Status</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentMembers as $member): ?>
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php
                            $imagePath = $member['image_path'] ?? '';
                            // Debug output
                            error_log("Member: " . $member['full_name'] . ", Original image path: " . $imagePath);
                            
                            // Remove any domain prefix if it exists
                            $imagePath = preg_replace('/^https?:\/\/[^\/]+\//', '', $imagePath);
                            error_log("After domain removal: " . $imagePath);
                            
                            $fullPath = "../" . $imagePath;
                            error_log("Full path to check: " . $fullPath);
                            error_log("File exists check: " . (file_exists($fullPath) ? "Yes" : "No"));
                            
                            // Check if image exists and is a valid path
                            if (!empty($imagePath) && file_exists($fullPath)) {
                                echo "<img src='../" . htmlspecialchars($imagePath) . "' alt='" . htmlspecialchars($member['full_name']) . "' class='member-avatar me-2'>";
                                error_log("Using member image: ../" . $imagePath);
                            } else {
                                echo "<img src='../assets/img/default-profile.jpg' alt='" . htmlspecialchars($member['full_name']) . "' class='member-avatar me-2'>";
                                error_log("Using default image for " . $member['full_name']);
                            }
                            ?>
                            <span class="member-name"><?php echo htmlspecialchars($member['full_name']); ?></span>
                        </div>
                    </td>
                    <td><span class="member-email"><?php echo htmlspecialchars($member['email']); ?></span></td>
                    <td>
                        <span class="member-joined">
                            <?php 
                                $joinDate = new DateTime($member['created_at']);
                                echo $joinDate->format('d M Y'); 
                            ?>
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="member-status status-active" title="Active"></span>
                    </td>
                </tr>
                <?php endforeach; ?>
                <?php if (count($recentMembers) == 0): ?>
                <tr>
                    <td colspan="4" class="text-center py-3">No recent members found</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    <div class="card-footer text-end">
        <a href="members.php" class="btn btn-sm btn-outline-primary">View All Members</a>
    </div>
</div>
<!-- End Recent Members Section -->

<!-- Email Test Card - Full Width and Robust -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card email-test-card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><i class="bi bi-envelope-check me-2"></i>Test Email Configuration</h5>
                <span class="badge bg-primary">SMTP Test</span>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-lg-8">
                        <p class="mb-0">
                            <i class="bi bi-info-circle me-2 text-primary"></i>
                            If you're having issues sending emails, you can test your email configuration here. 
                            A successful test confirms your SMTP settings are correct.
                        </p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-success py-2 mb-0">
                                <i class="bi bi-check-circle me-2"></i><?php echo $message; ?>
                            </div>
                        <?php elseif (!empty($error)): ?>
                            <div class="alert alert-danger py-2 mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <form method="POST" action="" class="email-test-form">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-5">
                            <label for="test_email" class="form-label">Test Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input type="email" class="form-control" id="test_email" name="test_email" placeholder="Enter email address to send test to" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="test_name" class="form-label">Recipient Name (Optional)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input type="text" class="form-control" id="test_name" name="test_name" placeholder="Recipient name">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                                <i class="bi bi-send-check me-2"></i> Send Test Email
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="mt-3">
                    <div class="accordion" id="emailTipsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingEmailTips">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEmailTips" aria-expanded="false" aria-controls="collapseEmailTips">
                                    <i class="bi bi-lightbulb me-2"></i> Email Troubleshooting Tips
                                </button>
                            </h2>
                            <div id="collapseEmailTips" class="accordion-collapse collapse" aria-labelledby="headingEmailTips" data-bs-parent="#emailTipsAccordion">
                                <div class="accordion-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Check that your SMTP server settings are correct in <a href="email_settings.php">Email Settings</a>
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Verify your server allows outgoing SMTP connections
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Make sure your SMTP username and password are correct
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Some email providers require application-specific passwords for SMTP access
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="members.php" class="btn btn-outline-primary w-100 p-3">
                            <i class="bi bi-people-fill fs-3 d-block mb-2"></i>
                            Manage Members
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="birthday.php" class="btn btn-outline-warning w-100 p-3">
                            <i class="bi bi-gift-fill fs-3 d-block mb-2"></i>
                            Birthday Messages
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="email_templates.php" class="btn btn-outline-info w-100 p-3">
                            <i class="bi bi-envelope-fill fs-3 d-block mb-2"></i>
                            Email Templates
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="email_analytics.php" class="btn btn-outline-success w-100 p-3">
                            <i class="bi bi-graph-up fs-3 d-block mb-2"></i>
                            Email Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Birthday Modal -->
<div class="modal fade" id="sendBirthdayModal" tabindex="-1" aria-labelledby="sendBirthdayModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendBirthdayModalLabel">Send Birthday Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sendBirthdayForm" action="send_birthday_message_fixed.php" method="post">
                    <input type="hidden" name="member_id" id="member_id">
                    
                    <div class="mb-3">
                        <label for="recipient_name" class="form-label">Recipient Name</label>
                        <input type="text" class="form-control" id="recipient_name" name="recipient_name" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="recipient_email" class="form-label">Recipient Email</label>
                        <input type="email" class="form-control" id="recipient_email" name="recipient_email" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template_id" class="form-label">Email Template</label>
                        <select class="form-select" id="template_id" name="template_id" required>
                            <option value="">Select a template</option>
                            <?php 
                            // Get birthday email templates
                            $conn = $pdo;
                            $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1");
                            $stmt->execute();
                            $emailTemplates = $stmt->fetchAll();
                            foreach ($emailTemplates as $template): 
                            ?>
                            <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['template_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">Email Subject</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="subject" name="subject" required>
                            <button class="btn btn-outline-secondary" type="button" id="show_template_subject" data-bs-toggle="tooltip" title="Use Template Subject">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="use_template_subject" name="use_template_subject" value="1">
                            <label class="form-check-label" for="use_template_subject">
                                Use Template Subject (supports {full_name} and other placeholders)
                            </label>
                        </div>
                        <small class="text-muted">You can use placeholders like {full_name}, {member_name} in the subject</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="custom_message" class="form-label">Custom Message (Optional)</label>
                        <textarea class="form-control" id="custom_message" name="custom_message" rows="3"></textarea>
                        <small class="text-muted">This will be added to the template.</small>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Send Birthday Message</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js library and initialization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var calendarEl = document.getElementById('calendar');
        
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            height: 'auto',
            aspectRatio: 1.1, // More compact aspect ratio (was 0.8)
            fixedWeekCount: true,
            headerToolbar: {
                left: 'prev,next',
                center: 'title',
                right: 'today'
            },
            dayMaxEvents: false,
            dayHeaderFormat: { weekday: 'narrow' }, // Single letter for more compact headers
            displayEventTime: false,
            events: [
                <?php
                try {
                    $conn = $pdo;
                    $stmt = $conn->prepare("SELECT id, full_name, birth_date, email FROM members WHERE birth_date IS NOT NULL");
                    $stmt->execute();
                    $allBirthdays = $stmt->fetchAll();
                    
                    foreach ($allBirthdays as $member):
                        if (!empty($member['birth_date'])):
                            $birthDate = new DateTime($member['birth_date']);
                            $birthMonth = $birthDate->format('m');
                            $birthDay = $birthDate->format('d');
                            
                            $birthdayThisYear = date('Y') . '-' . $birthMonth . '-' . $birthDay;
                ?>
                {
                    title: '🎂 <?php echo htmlspecialchars($member['full_name']); ?>',
                    start: '<?php echo $birthdayThisYear; ?>',
                    allDay: true,
                    backgroundColor: '#ffc107',
                    borderColor: '#ffc107',
                    textColor: '#000000',
                    classNames: ['birthday-event'],
                    extendedProps: {
                        type: 'birthday',
                        memberId: <?php echo $member['id']; ?>
                    }
                },
                <?php
                        endif;
                    endforeach;
                } catch (Exception $e) {
                    error_log("Error fetching birthdays: " . $e->getMessage());
                }
                ?>
            ],
            eventContent: function(arg) {
                let eventTitle = document.createElement('div');
                if (arg.event.extendedProps.type === 'birthday') {
                    eventTitle.innerHTML = arg.event.title;
                    eventTitle.className = 'fc-event-title birthday-title';
                } else {
                    eventTitle.innerHTML = arg.event.title;
                    eventTitle.className = 'fc-event-title';
                }
                
                return { domNodes: [eventTitle] };
            },
            eventClick: function(info) {
                if (info.event.extendedProps.type === 'birthday') {
                    var memberId = info.event.extendedProps.memberId;
                    var buttons = document.querySelectorAll('.send-birthday-btn');
                    for (var i = 0; i < buttons.length; i++) {
                        if (parseInt(buttons[i].getAttribute('data-id')) === memberId) {
                            buttons[i].click();
                            break;
                        }
                    }
                }
            },
            viewDidMount: function(view) {
                setTimeout(function() {
                    // Fix calendar display issues
                    document.querySelectorAll('.fc-scroller').forEach(function(el) {
                        el.style.overflow = 'visible';
                        el.style.height = 'auto';
                    });
                    
                    document.querySelectorAll('.fc-scrollgrid-section table').forEach(function(table) {
                        table.style.height = 'auto';
                    });
                    
                    // Ensure proper calendar height and no overflow
                    const viewHarness = document.querySelector('.fc-view-harness');
                    if (viewHarness) {
                        viewHarness.style.height = 'auto';
                        viewHarness.style.maxHeight = '310px';
                    }
                    
                    // Make days compact to fit all dates
                    document.querySelectorAll('.fc-daygrid-day').forEach(function(day) {
                        day.style.height = '45px';
                        day.style.minHeight = '45px';
                        day.style.maxHeight = '45px';
                    });
                    
                    // Make day frames compact
                    document.querySelectorAll('.fc-daygrid-day-frame').forEach(function(frame) {
                        frame.style.height = '45px';
                        frame.style.minHeight = '45px';
                        frame.style.maxHeight = '45px';
                        frame.style.overflow = 'hidden';
                    });
                    
                    // Handle event display in compact cells
                    document.querySelectorAll('.fc-daygrid-day-events').forEach(function(events) {
                        events.style.margin = '0';
                        events.style.padding = '0';
                        events.style.maxHeight = '25px';
                        events.style.overflow = 'hidden';
                    });
                    
                    // Match heights of calendar and birthday cards
                    matchHeights('.calendar-card', '.birthday-card');
                }, 100);
            }
        });
        
        // Store calendar instance in a global variable for access from other scripts
        window.mainCalendar = calendar;
        
        calendar.render();
        
        // Initialize charts
        initializeCharts();
        
        // Function to match heights of two elements
        function matchHeights(selector1, selector2) {
            const el1 = document.querySelector(selector1);
            const el2 = document.querySelector(selector2);
            if (el1 && el2) {
                const height = Math.max(el1.offsetHeight, el2.offsetHeight);
                el1.style.height = height + 'px';
                el2.style.height = height + 'px';
            }
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            calendar.updateSize();
            setTimeout(function() {
                matchHeights('.calendar-card', '.birthday-card');
            }, 100);
        });
        
        // Add Cron Job Status Check
        const checkCronStatus = document.getElementById('checkCronStatus');
        const cronStatusAlert = document.getElementById('cronStatusAlert');
        
        function updateCronStatus(success, message) {
            cronStatusAlert.classList.remove('d-none', 'alert-success', 'alert-danger', 'alert-warning');
            cronStatusAlert.classList.add(success === true ? 'alert-success' : 
                                        success === false ? 'alert-danger' : 
                                        'alert-warning');
            
            let icon = success === true ? '<i class="bi bi-check-circle me-2"></i>' : 
                       success === false ? '<i class="bi bi-exclamation-triangle me-2"></i>' :
                       '<i class="bi bi-info-circle me-2"></i>';
                
            cronStatusAlert.innerHTML = `${icon}${message}`;
        }
        
        checkCronStatus.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Checking...';
            
            // Get the base URL from the meta tag
            const siteUrl = document.querySelector('meta[name="debug-site-url"]').content;
            const environment = document.querySelector('meta[name="debug-env"]').content;
            
            // Construct the cron URL based on environment
            const cronPath = siteUrl + '/birthday_reminders.php';
            const proxyPath = siteUrl + '/admin/proxy.php';
            
            // Use the proxy endpoint
            fetch(proxyPath + '?action=check_cron', {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                // Try to parse the response as JSON
                try {
                    const data = JSON.parse(text);
                    if (data.status === 'success') {
                        updateCronStatus(true, 'Cron job is working correctly. Found ' + 
                            (data.upcoming_birthdays || 0) + ' upcoming birthdays. Last check: ' + 
                            (data.timestamp || new Date().toLocaleString()));
                    } else {
                        // Show detailed error for debugging
                        let errorMsg = 'Error: ' + (data.message || 'Unknown error');
                        if (environment === 'development' || environment === 'staging') {
                            if (data.debug) {
                                errorMsg += '<br><small class="text-muted">Debug info: ' + 
                                    JSON.stringify(data.debug) + '</small>';
                            }
                        }
                        updateCronStatus(false, errorMsg);
                    }
                } catch (e) {
                    console.error('Failed to parse JSON:', text);
                    // If the response is HTML, show a more helpful error
                    let errorMsg = 'Invalid response from server: ' + e.message;
                    if (text.includes('Fatal error') || text.includes('<b>')) {
                        // Extract the error message from the HTML
                        const errorMatch = text.match(/<b>Fatal error<\/b>:(.+?)<br/);
                        if (errorMatch && errorMatch[1]) {
                            errorMsg = 'Server error: ' + errorMatch[1].trim();
                        } else {
                            errorMsg = 'Server returned an HTML error page instead of JSON';
                        }
                    }
                    updateCronStatus(false, errorMsg);
                }
            })
            .catch(error => {
                console.error('Cron check error:', error);
                updateCronStatus(false, 'Error checking cron status: ' + error.message);
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Check Status';
            });
        });
        
        // Auto-check status on page load with a delay
        setTimeout(() => checkCronStatus.click(), 1000);
    });
    
    // Initialize charts with organization data
    function initializeCharts() {
        // Set custom chart colors
        const chartColors = {
            primary: '#4e73df',
            success: '#1cc88a',
            info: '#36b9cc',
            warning: '#f6c23e',
            danger: '#e74a3b',
            secondary: '#858796',
            purple: '#6f42c1',
            pink: '#e83e8c',
            orange: '#fd7e14',
            teal: '#20c9a6'
        };
        
        // Generate gradient for the charts
        function createGradient(ctx, colorStart, colorEnd) {
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, colorStart);
            gradient.addColorStop(1, colorEnd);
            return gradient;
        }
        
        // 1. Member Growth Chart - Enhanced with dual axes and annotations
        const memberGrowthCtx = document.getElementById('memberGrowthChart').getContext('2d');
        const memberGrowthGradient = createGradient(memberGrowthCtx, 'rgba(78, 115, 223, 0.2)', 'rgba(78, 115, 223, 0)');
        const memberGrowthChart = new Chart(memberGrowthCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [
                    {
                        label: 'New Members',
                        data: [5, 8, 12, 7, 10, 15, 18, 14, 12, 16, 9, 11],
                        borderColor: chartColors.primary,
                        backgroundColor: memberGrowthGradient,
                        borderWidth: 3,
                        pointBackgroundColor: chartColors.primary,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.4,
                        fill: true,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Attendance',
                        data: [45, 60, 75, 80, 94, 98, 110, 102, 95, 105, 115, 125],
                        borderColor: chartColors.success,
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        borderDash: [5, 5],
                        pointBackgroundColor: chartColors.success,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        tension: 0.4,
                        fill: false,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#6e707e',
                        bodyColor: '#6e707e',
                        borderColor: '#e3e6f0',
                        borderWidth: 1,
                        padding: 12,
                        displayColors: true,
                        usePointStyle: true,
                        callbacks: {
                            labelPointStyle: function(context) {
                                return {
                                    pointStyle: 'circle',
                                    rotation: 0
                                };
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'New Members',
                            color: chartColors.primary,
                            font: {
                                weight: 'bold'
                            }
                        },
                        beginAtZero: true,
                        grid: {
                            drawBorder: false,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Attendance',
                            color: chartColors.success,
                            font: {
                                weight: 'bold'
                            }
                        },
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // 2. Age Distribution Chart - Enhanced with polarArea chart
        const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
        const ageDistributionChart = new Chart(ageDistributionCtx, {
            type: 'polarArea',
            data: {
                labels: ['0-12', '13-18', '19-25', '26-35', '36-50', '51-65', '65+'],
                datasets: [{
                    data: [15, 12, 25, 38, 30, 20, 10],
                    backgroundColor: [
                        chartColors.info,
                        chartColors.purple,
                        chartColors.primary,
                        chartColors.success,
                        chartColors.warning,
                        chartColors.orange,
                        chartColors.pink
                    ],
                    borderWidth: 1,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.raw + ' members';
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        ticks: {
                            display: false
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // 3. Birthday Distribution Chart - Enhanced with animation and labels
        const birthdayDistributionCtx = document.getElementById('birthdayDistributionChart').getContext('2d');
        const birthdayDistributionChart = new Chart(birthdayDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Jan-Mar', 'Apr-Jun', 'Jul-Sep', 'Oct-Dec'],
                datasets: [{
                    data: [
                        <?php 
                            // Count birthdays by quarter
                            $q1 = $q2 = $q3 = $q4 = 0;
                            foreach ($allBirthdays as $member) {
                                if (!empty($member['birth_date'])) {
                                    $birthMonth = (new DateTime($member['birth_date']))->format('m');
                                    if ($birthMonth >= 1 && $birthMonth <= 3) $q1++;
                                    elseif ($birthMonth >= 4 && $birthMonth <= 6) $q2++;
                                    elseif ($birthMonth >= 7 && $birthMonth <= 9) $q3++;
                                    elseif ($birthMonth >= 10 && $birthMonth <= 12) $q4++;
                                }
                            }
                            echo "$q1, $q2, $q3, $q4";
                        ?>
                    ],
                    backgroundColor: [
                        chartColors.primary,
                        chartColors.success,
                        chartColors.warning,
                        chartColors.danger
                    ],
                    hoverBackgroundColor: [
                        '#3a5ccc',
                        '#16a96e',
                        '#e6b014',
                        '#d13926'
                    ],
                    borderWidth: 0,
                    hoverOffset: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 2000
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return context.label + ': ' + context.raw + ' members (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
        
        // 4. Email Engagement Chart - Radar chart for multiple metrics
        const emailEngagementCtx = document.getElementById('emailEngagementChart').getContext('2d');
        const emailEngagementChart = new Chart(emailEngagementCtx, {
            type: 'radar',
            data: {
                labels: ['Open Rate', 'Click Rate', 'Response', 'Unsubscribe', 'Forwarded'],
                datasets: [{
                    label: 'Birthday Emails',
                    data: [85, 65, 40, 5, 30],
                    backgroundColor: 'rgba(78, 115, 223, 0.2)',
                    borderColor: chartColors.primary,
                    pointBackgroundColor: chartColors.primary,
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: chartColors.primary,
                    borderWidth: 2
                }, {
                    label: 'Event Emails',
                    data: [75, 55, 25, 3, 45],
                    backgroundColor: 'rgba(28, 200, 138, 0.2)',
                    borderColor: chartColors.success,
                    pointBackgroundColor: chartColors.success,
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: chartColors.success,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                elements: {
                    line: {
                        tension: 0.1
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        pointLabels: {
                            font: {
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            backdropColor: 'transparent',
                            showLabelBackdrop: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.raw + '%';
                            }
                        }
                    }
                }
            }
        });
        
        // 5. Membership Status Chart - Gauge chart for membership status
        const membershipStatusCtx = document.getElementById('membershipStatusChart').getContext('2d');
        const membershipStatusChart = new Chart(membershipStatusCtx, {
            type: 'pie',
            data: {
                labels: ['Active', 'New Members', 'Inactive', 'Visitors'],
                datasets: [{
                    data: [65, 15, 10, 10],
                    backgroundColor: [
                        chartColors.success,
                        chartColors.primary,
                        chartColors.secondary,
                        chartColors.warning
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return context.label + ': ' + percentage + '%';
                            }
                        }
                    }
                }
            }
        });
    }
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Additional script to ensure calendar displays all days
        function fixCalendarDisplay() {
            // Remove maximum height constraints
            const calendarEl = document.getElementById('calendar');
            if (calendarEl) {
                calendarEl.style.maxHeight = 'none';
                
                // Make sure all table rows are visible
                document.querySelectorAll('.fc-scrollgrid-sync-table').forEach(function(table) {
                    table.style.height = 'auto';
                });
                
                // Remove any height constraints on the view container
                document.querySelectorAll('.fc-view-harness').forEach(function(el) {
                    el.style.height = 'auto !important';
                });
                
                // Ensure calendar body has no height constraints
                document.querySelectorAll('.fc-daygrid-body').forEach(function(el) {
                    el.style.height = 'auto';
                    el.style.overflowY = 'visible';
                });
                
                // Fix any padding/margin issues within cells
                document.querySelectorAll('.fc-daygrid-day-frame').forEach(function(cell) {
                    cell.style.height = 'auto';
                    cell.style.overflow = 'visible';
                });
            }
        }
        
        // Run after a slight delay to ensure calendar is fully rendered
        setTimeout(fixCalendarDisplay, 200);
        
        // Also run when window is resized
        window.addEventListener('resize', function() {
            setTimeout(fixCalendarDisplay, 100);
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Additional script to make calendar compact after rendering
        function makeCalendarCompact() {
            // Force calendar to be compact
            document.querySelectorAll('.fc-daygrid-day').forEach(function(day) {
                if (day.style.height) {
                    day.style.height = 'min-content';
                }
            });
            
            // Set maximum height for the calendar container
            document.querySelector('.fc-view-harness').style.maxHeight = '350px';
            
            // Make sure all events display properly
            document.querySelectorAll('.fc-daygrid-event-harness').forEach(function(event) {
                event.style.marginTop = '1px';
                event.style.marginBottom = '1px';
            });
        }
        
        // Run the function after calendar is rendered with a delay
        setTimeout(makeCalendarCompact, 300);
        
        // Also run on window resize
        window.addEventListener('resize', function() {
            setTimeout(makeCalendarCompact, 100);
        });
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Add error logging
        window.onerror = function(message, source, lineno, colno, error) {
            console.error("JavaScript Error:", message, "at", source, "line:", lineno, "column:", colno, "error:", error);
            return false;
        };
        
        // Store template data for easy access
        const templateData = {};
        <?php foreach ($emailTemplates as $t): ?>
        templateData["<?php echo $t['id']; ?>"] = {
            "name": <?php echo json_encode($t['template_name'] ?? ''); ?>,
            "subject": <?php echo json_encode($t['subject'] ?? ''); ?>
        };
        <?php endforeach; ?>
        
        console.log("Template data loaded:", Object.keys(templateData).length, "templates");
        
        // Handle template selection change
        const templateSelect = document.getElementById("template_id");
        const subjectField = document.getElementById("subject");
        const useTemplateSubjectCheckbox = document.getElementById("use_template_subject");
        const showTemplateSubjectBtn = document.getElementById("show_template_subject");
        
        if (templateSelect && subjectField) {
            templateSelect.addEventListener("change", function() {
                const templateId = this.value;
                
                if (templateId && useTemplateSubjectCheckbox && useTemplateSubjectCheckbox.checked) {
                    if (templateData[templateId]) {
                        subjectField.value = templateData[templateId].subject;
                    }
                }
            });
        }
        
        // Handle use template subject checkbox
        if (useTemplateSubjectCheckbox) {
            useTemplateSubjectCheckbox.addEventListener("change", function() {
                try {
                    const templateId = templateSelect.value;
                    
                    if (this.checked && templateId && templateData[templateId]) {
                        if (!subjectField.dataset.manualSubject) {
                            subjectField.dataset.manualSubject = subjectField.value;
                        }
                        subjectField.value = templateData[templateId].subject;
                        subjectField.readOnly = true;
                    } else {
                        if (subjectField.dataset.manualSubject) {
                            subjectField.value = subjectField.dataset.manualSubject;
                        }
                        subjectField.readOnly = false;
                    }
                } catch (error) {
                    console.error("Error in template subject checkbox handler:", error);
                }
            });
        }
        
        // Handle show template subject button
        if (showTemplateSubjectBtn) {
            showTemplateSubjectBtn.addEventListener("click", function() {
                try {
                    const templateId = templateSelect.value;
                    
                    if (templateId && templateData[templateId]) {
                        if (!useTemplateSubjectCheckbox.checked) {
                            subjectField.dataset.manualSubject = subjectField.value;
                        }
                        
                        if (subjectField.value === templateData[templateId].subject) {
                            if (subjectField.dataset.manualSubject) {
                                subjectField.value = subjectField.dataset.manualSubject;
                            }
                            useTemplateSubjectCheckbox.checked = false;
                            subjectField.readOnly = false;
                        } else {
                            subjectField.value = templateData[templateId].subject;
                            useTemplateSubjectCheckbox.checked = true;
                            subjectField.readOnly = true;
                        }
                    }
                } catch (error) {
                    console.error("Error in template subject button handler:", error);
                }
            });
        }
        
        // Handle birthday button click
        const sendBirthdayBtns = document.querySelectorAll(".send-birthday-btn");
        sendBirthdayBtns.forEach(btn => {
            btn.addEventListener("click", function() {
                try {
                    const id = this.getAttribute("data-id");
                    const name = this.getAttribute("data-name");
                    const email = this.getAttribute("data-email");
                    
                    console.log("Button clicked for:", id, name, email);
                    
                    document.getElementById("member_id").value = id;
                    document.getElementById("recipient_name").value = name;
                    document.getElementById("recipient_email").value = email;
                    
                    // Set default birthday subject
                    const firstName = name.split(" ")[0];
                    const defaultSubject = "Happy Birthday, " + firstName + "!";
                    document.getElementById("subject").value = defaultSubject;
                    document.getElementById("subject").dataset.manualSubject = defaultSubject;
                    
                    // Reset form fields
                    document.getElementById("template_id").value = "";
                    document.getElementById("custom_message").value = "";
                    document.getElementById("use_template_subject").checked = false;
                    document.getElementById("subject").readOnly = false;
                    
                } catch (e) {
                    console.error("Error in button click handler:", e);
                    alert("Error: " + e.message);
                }
            });
        });

        // Handle form submission
        const birthdayForm = document.getElementById("sendBirthdayForm");
        if (birthdayForm) {
            birthdayForm.addEventListener("submit", function(e) {
                try {
                    console.log("Form submission started");
                    
                    e.preventDefault();
                    
                    const formData = new FormData(birthdayForm);
                    console.log("Form data:");
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ": " + pair[1]);
                    }
                    
                    if (!birthdayForm.checkValidity()) {
                        console.log("Form validation failed");
                        e.stopPropagation();
                        birthdayForm.classList.add("was-validated");
                        return;
                    }
                    
                    console.log("Form is valid, submitting...");
                    
                    const submitBtn = birthdayForm.querySelector("button[type='submit']");
                    const originalBtnText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
                    submitBtn.disabled = true;
                    
                    const recipientName = document.getElementById("recipient_name").value;
                    const tempMessage = document.createElement("div");
                    tempMessage.className = "alert alert-info mt-3";
                    tempMessage.innerHTML = '<i class="bi bi-info-circle-fill me-2"></i>Sending birthday message to ' + recipientName + "...";
                    birthdayForm.appendChild(tempMessage);
                    
                    birthdayForm.submit();
                } catch (e) {
                    console.error("Error in form submission:", e);
                    alert("Error: " + e.message);
                }
            });
        }
        
    } catch (error) {
        console.error("Error in DOMContentLoaded:", error);
        alert("Error: " + error.message);
    }
});
</script>

<?php include 'includes/footer.php'; ?>