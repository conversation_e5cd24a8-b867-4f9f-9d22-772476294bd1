<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$results = [];

try {
    // Create volunteer_opportunities table
    $sql = "CREATE TABLE IF NOT EXISTS `volunteer_opportunities` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `description` text NOT NULL,
        `category` varchar(100) NOT NULL,
        `required_skills` text DEFAULT NULL COMMENT 'JSON array of required skill IDs',
        `preferred_skills` text DEFAULT NULL COMMENT 'JSON array of preferred skill IDs',
        `time_commitment` varchar(255) DEFAULT NULL,
        `schedule_type` enum('one_time','recurring','flexible','ongoing') NOT NULL DEFAULT 'one_time',
        `start_date` date DEFAULT NULL,
        `end_date` date DEFAULT NULL,
        `location` varchar(255) DEFAULT NULL,
        `contact_person_id` int(11) NOT NULL,
        `max_volunteers` int(11) DEFAULT NULL,
        `min_age` int(11) DEFAULT NULL,
        `background_check_required` tinyint(1) NOT NULL DEFAULT 0,
        `training_required` tinyint(1) NOT NULL DEFAULT 0,
        `training_description` text DEFAULT NULL,
        `status` enum('active','inactive','completed','cancelled') NOT NULL DEFAULT 'active',
        `created_by` int(11) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_category` (`category`),
        KEY `idx_status` (`status`),
        KEY `idx_contact_person` (`contact_person_id`),
        KEY `idx_created_by` (`created_by`),
        CONSTRAINT `fk_volunteer_contact` FOREIGN KEY (`contact_person_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_volunteer_creator` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    $results[] = "✅ volunteer_opportunities table created successfully";

    // Create volunteer_applications table
    $sql = "CREATE TABLE IF NOT EXISTS `volunteer_applications` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `opportunity_id` int(11) NOT NULL,
        `member_id` int(11) NOT NULL,
        `application_message` text DEFAULT NULL,
        `availability` text DEFAULT NULL,
        `relevant_experience` text DEFAULT NULL,
        `status` enum('pending','approved','rejected','withdrawn') NOT NULL DEFAULT 'pending',
        `reviewed_by` int(11) DEFAULT NULL,
        `reviewed_at` datetime DEFAULT NULL,
        `review_notes` text DEFAULT NULL,
        `start_date` date DEFAULT NULL,
        `end_date` date DEFAULT NULL,
        `hours_committed` decimal(5,2) DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_application` (`opportunity_id`,`member_id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_status` (`status`),
        KEY `idx_reviewed_by` (`reviewed_by`),
        CONSTRAINT `fk_application_opportunity` FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_application_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_application_reviewer` FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    $results[] = "✅ volunteer_applications table created successfully";

    // Create volunteer_hours table
    $sql = "CREATE TABLE IF NOT EXISTS `volunteer_hours` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `opportunity_id` int(11) DEFAULT NULL,
        `date` date NOT NULL,
        `hours` decimal(5,2) NOT NULL,
        `description` text DEFAULT NULL,
        `verified_by` int(11) DEFAULT NULL,
        `verified_at` datetime DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_opportunity_id` (`opportunity_id`),
        KEY `idx_date` (`date`),
        KEY `idx_verified_by` (`verified_by`),
        CONSTRAINT `fk_hours_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_hours_opportunity` FOREIGN KEY (`opportunity_id`) REFERENCES `volunteer_opportunities` (`id`) ON DELETE SET NULL,
        CONSTRAINT `fk_hours_verifier` FOREIGN KEY (`verified_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    $results[] = "✅ volunteer_hours table created successfully";

    // Create member_skills table
    $sql = "CREATE TABLE IF NOT EXISTS `member_skills` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `skill_name` varchar(100) NOT NULL,
        `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
        `years_experience` int(11) DEFAULT NULL,
        `description` text DEFAULT NULL,
        `verified` tinyint(1) NOT NULL DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_member_skill` (`member_id`,`skill_name`),
        KEY `idx_skill_name` (`skill_name`),
        KEY `idx_proficiency` (`proficiency_level`),
        CONSTRAINT `fk_skill_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    $results[] = "✅ member_skills table created successfully";

    // Check if volunteer_interests column exists in members table, add if missing
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'volunteer_interests'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE members ADD COLUMN volunteer_interests TEXT DEFAULT NULL COMMENT 'JSON array of volunteer interest categories'");
        $results[] = "✅ volunteer_interests column added to members table";
    } else {
        $results[] = "✅ volunteer_interests column already exists in members table";
    }

    // Check if availability_schedule column exists in members table, add if missing
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'availability_schedule'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE members ADD COLUMN availability_schedule TEXT DEFAULT NULL COMMENT 'When member is available for volunteer activities'");
        $results[] = "✅ availability_schedule column added to members table";
    } else {
        $results[] = "✅ availability_schedule column already exists in members table";
    }

    // Insert sample volunteer opportunities if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM volunteer_opportunities");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Get first admin and first member for sample data
        $stmt = $pdo->query("SELECT id FROM admins LIMIT 1");
        $admin_id = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT id FROM members WHERE status = 'active' LIMIT 1");
        $member_id = $stmt->fetchColumn();
        
        if ($admin_id && $member_id) {
            $sample_opportunities = [
                [
                    'title' => 'Sunday Service Greeter',
                    'description' => 'Welcome visitors and members as they arrive for Sunday services. Help create a warm, friendly atmosphere.',
                    'category' => 'ministry',
                    'time_commitment' => '2 hours per Sunday',
                    'schedule_type' => 'recurring',
                    'location' => 'Main Entrance',
                    'contact_person_id' => $member_id,
                    'max_volunteers' => 4,
                    'min_age' => 16,
                    'created_by' => $admin_id
                ],
                [
                    'title' => 'Children\'s Ministry Helper',
                    'description' => 'Assist with children\'s programs during Sunday services. Help with activities, crafts, and supervision.',
                    'category' => 'children',
                    'required_skills' => '["childcare", "patience", "creativity"]',
                    'time_commitment' => '3 hours per Sunday',
                    'schedule_type' => 'recurring',
                    'location' => 'Children\'s Wing',
                    'contact_person_id' => $member_id,
                    'max_volunteers' => 6,
                    'min_age' => 18,
                    'background_check_required' => 1,
                    'training_required' => 1,
                    'training_description' => 'Child safety and ministry training required',
                    'created_by' => $admin_id
                ],
                [
                    'title' => 'Community Outreach Event',
                    'description' => 'Help organize and run community outreach events. Assist with setup, food distribution, and cleanup.',
                    'category' => 'outreach',
                    'time_commitment' => '4-6 hours per event',
                    'schedule_type' => 'one_time',
                    'start_date' => date('Y-m-d', strtotime('+2 weeks')),
                    'location' => 'Community Center',
                    'contact_person_id' => $member_id,
                    'max_volunteers' => 15,
                    'min_age' => 14,
                    'created_by' => $admin_id
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO volunteer_opportunities 
                (title, description, category, required_skills, time_commitment, schedule_type, 
                 start_date, location, contact_person_id, max_volunteers, min_age, 
                 background_check_required, training_required, training_description, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($sample_opportunities as $opp) {
                $stmt->execute([
                    $opp['title'],
                    $opp['description'],
                    $opp['category'],
                    $opp['required_skills'] ?? null,
                    $opp['time_commitment'],
                    $opp['schedule_type'],
                    $opp['start_date'] ?? null,
                    $opp['location'],
                    $opp['contact_person_id'],
                    $opp['max_volunteers'],
                    $opp['min_age'],
                    $opp['background_check_required'] ?? 0,
                    $opp['training_required'] ?? 0,
                    $opp['training_description'] ?? null,
                    $opp['created_by']
                ]);
            }
            
            $results[] = "✅ Sample volunteer opportunities created";
        }
    } else {
        $results[] = "✅ Volunteer opportunities already exist ($count found)";
    }

} catch (PDOException $e) {
    $results[] = "❌ Error: " . $e->getMessage();
}

// Set page variables
$page_title = 'Setup Volunteer Tables';
$page_header = 'Volunteer System Setup';
$page_description = 'Initialize volunteer management system tables and data';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-database-gear"></i> Volunteer System Setup Results</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($results as $result): ?>
                        <div class="alert alert-<?php echo strpos($result, '✅') !== false ? 'success' : 'danger'; ?>">
                            <?php echo $result; ?>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="mt-4">
                        <a href="volunteer_opportunities.php" class="btn btn-primary">
                            <i class="bi bi-person-workspace"></i> Go to Volunteer Opportunities
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="bi bi-house"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
