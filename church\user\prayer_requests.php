<?php
/**
 * Prayer Request Tracking System
 * Personal prayer request management with privacy controls and community sharing
 */

session_start();
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_prayer_request'])) {
            // Add new prayer request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_requests 
                (member_id, title, description, category, privacy_level, is_urgent, is_anonymous)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['privacy_level'],
                isset($_POST['is_urgent']) ? 1 : 0,
                isset($_POST['is_anonymous']) ? 1 : 0
            ]);
            
            $message = "Prayer request added successfully!";
        }
        
        if (isset($_POST['update_prayer_request'])) {
            // Update existing prayer request
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET title = ?, description = ?, category = ?, privacy_level = ?, 
                    is_urgent = ?, is_anonymous = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND member_id = ?
            ");
            
            $stmt->execute([
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['privacy_level'],
                isset($_POST['is_urgent']) ? 1 : 0,
                isset($_POST['is_anonymous']) ? 1 : 0,
                $_POST['prayer_request_id'],
                $userId
            ]);
            
            $message = "Prayer request updated successfully!";
        }
        
        if (isset($_POST['update_status'])) {
            // Update prayer request status
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND member_id = ?
            ");
            
            $stmt->execute([
                $_POST['status'],
                $_POST['prayer_request_id'],
                $userId
            ]);
            
            $message = "Prayer request status updated!";
        }
        
        if (isset($_POST['add_prayer_response'])) {
            // Add prayer response
            $stmt = $pdo->prepare("
                INSERT INTO prayer_responses 
                (prayer_request_id, member_id, response_type, comment)
                VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['prayer_request_id'],
                $userId,
                $_POST['response_type'],
                $_POST['comment'] ?? ''
            ]);
            
            $message = "Thank you for your prayer response!";
        }
        
    } catch (PDOException $e) {
        $error = "Error managing prayer requests: " . $e->getMessage();
    }
}

// Get user's prayer requests
try {
    $stmt = $pdo->prepare("
        SELECT pr.*, 
               COUNT(DISTINCT prep.id) as prayer_count,
               COUNT(DISTINCT CASE WHEN prep.response_type = 'prayed' THEN prep.id END) as prayed_count
        FROM prayer_requests pr
        LEFT JOIN prayer_responses prep ON pr.id = prep.prayer_request_id
        WHERE pr.member_id = ?
        GROUP BY pr.id
        ORDER BY pr.is_urgent DESC, pr.created_at DESC
    ");
    $stmt->execute([$userId]);
    $my_prayer_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $my_prayer_requests = [];
    $error = "Error loading prayer requests: " . $e->getMessage();
}

// Get community prayer requests (public and members-only)
try {
    $stmt = $pdo->prepare("
        SELECT pr.*, m.full_name as requester_name,
               COUNT(DISTINCT prep.id) as prayer_count,
               COUNT(DISTINCT CASE WHEN prep.response_type = 'prayed' THEN prep.id END) as prayed_count,
               MAX(CASE WHEN prep.member_id = ? THEN prep.response_type END) as my_response
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prep ON pr.id = prep.prayer_request_id
        WHERE pr.member_id != ? 
        AND pr.privacy_level IN ('public', 'members')
        AND pr.status = 'active'
        GROUP BY pr.id
        ORDER BY pr.is_urgent DESC, pr.created_at DESC
        LIMIT 20
    ");
    $stmt->execute([$userId, $userId]);
    $community_prayer_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $community_prayer_requests = [];
}

// Get user data
$userData = $userAuth->getUserById($userId);

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Requests - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <?php include 'includes/theme_css.php'; ?>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }
        
        .prayer-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.2s ease;
        }
        
        .prayer-card:hover {
            transform: translateY(-2px);
        }
        
        .prayer-card.urgent {
            border-left: 5px solid #dc3545;
        }
        
        .prayer-card.answered {
            border-left: 5px solid #28a745;
        }
        
        .prayer-stats {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .privacy-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .category-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto"></div>
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="prayer_requests.php">
                            <i class="bi bi-heart"></i> Prayer Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="prayer_requests.php"><i class="bi bi-heart"></i> Prayer Requests</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-heart-fill"></i> Prayer Requests</h2>
                <p class="text-muted">Share your prayer needs and pray for others</p>
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPrayerModal">
                <i class="bi bi-plus-circle"></i> Add Prayer Request
            </button>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Prayer Statistics -->
        <div class="prayer-stats">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($my_prayer_requests); ?></div>
                        <div class="stat-label">My Requests</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count(array_filter($my_prayer_requests, function($pr) { return $pr['status'] === 'active'; })); ?></div>
                        <div class="stat-label">Active</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count(array_filter($my_prayer_requests, function($pr) { return $pr['status'] === 'answered'; })); ?></div>
                        <div class="stat-label">Answered</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo array_sum(array_column($my_prayer_requests, 'prayer_count')); ?></div>
                        <div class="stat-label">Total Prayers</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prayer Request Tabs -->
        <ul class="nav nav-tabs mb-4" id="prayerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="my-prayers-tab" data-bs-toggle="tab" data-bs-target="#my-prayers" type="button" role="tab">
                    <i class="bi bi-person"></i> My Prayer Requests (<?php echo count($my_prayer_requests); ?>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="community-prayers-tab" data-bs-toggle="tab" data-bs-target="#community-prayers" type="button" role="tab">
                    <i class="bi bi-people"></i> Community Prayers (<?php echo count($community_prayer_requests); ?>)
                </button>
            </li>
        </ul>

        <div class="tab-content" id="prayerTabsContent">
            <!-- My Prayer Requests Tab -->
            <div class="tab-pane fade show active" id="my-prayers" role="tabpanel">
                <?php if (empty($my_prayer_requests)): ?>
                    <div class="prayer-card text-center">
                        <i class="bi bi-heart text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 text-muted">No Prayer Requests Yet</h4>
                        <p class="text-muted">Share your prayer needs with the community or keep them private for personal tracking.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPrayerModal">
                            <i class="bi bi-plus-circle"></i> Add Your First Prayer Request
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($my_prayer_requests as $prayer): ?>
                        <div class="prayer-card <?php echo $prayer['is_urgent'] ? 'urgent' : ''; ?> <?php echo $prayer['status'] === 'answered' ? 'answered' : ''; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <h5 class="mb-2">
                                        <?php echo htmlspecialchars($prayer['title']); ?>
                                        <?php if ($prayer['is_urgent']): ?>
                                            <span class="badge bg-danger ms-2">Urgent</span>
                                        <?php endif; ?>
                                    </h5>
                                    <div class="d-flex gap-2 mb-2">
                                        <span class="badge bg-primary category-badge"><?php echo ucfirst($prayer['category']); ?></span>
                                        <span class="badge bg-secondary privacy-badge"><?php echo ucfirst($prayer['privacy_level']); ?></span>
                                        <span class="badge bg-<?php echo $prayer['status'] === 'answered' ? 'success' : ($prayer['status'] === 'active' ? 'info' : 'secondary'); ?> privacy-badge">
                                            <?php echo ucfirst($prayer['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editPrayer(<?php echo htmlspecialchars(json_encode($prayer)); ?>)">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus(<?php echo $prayer['id']; ?>, '<?php echo htmlspecialchars($prayer['title']); ?>')">
                                            <i class="bi bi-check-circle"></i> Update Status
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <p class="mb-3"><?php echo nl2br(htmlspecialchars($prayer['description'])); ?></p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($prayer['created_at'])); ?>
                                    <?php if ($prayer['updated_at'] !== $prayer['created_at']): ?>
                                        | Updated <?php echo date('M j, Y', strtotime($prayer['updated_at'])); ?>
                                    <?php endif; ?>
                                </small>
                                <div class="text-muted">
                                    <i class="bi bi-heart-fill"></i> <?php echo $prayer['prayed_count']; ?> prayers
                                    | <i class="bi bi-chat"></i> <?php echo $prayer['prayer_count']; ?> responses
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Community Prayer Requests Tab -->
            <div class="tab-pane fade" id="community-prayers" role="tabpanel">
                <?php if (empty($community_prayer_requests)): ?>
                    <div class="prayer-card text-center">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 text-muted">No Community Prayer Requests</h4>
                        <p class="text-muted">No one has shared prayer requests with the community yet.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($community_prayer_requests as $prayer): ?>
                        <div class="prayer-card <?php echo $prayer['is_urgent'] ? 'urgent' : ''; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <h5 class="mb-2">
                                        <?php echo htmlspecialchars($prayer['title']); ?>
                                        <?php if ($prayer['is_urgent']): ?>
                                            <span class="badge bg-danger ms-2">Urgent</span>
                                        <?php endif; ?>
                                    </h5>
                                    <div class="d-flex gap-2 mb-2">
                                        <span class="badge bg-primary category-badge"><?php echo ucfirst($prayer['category']); ?></span>
                                        <?php if (!$prayer['is_anonymous']): ?>
                                            <span class="badge bg-info privacy-badge">by <?php echo htmlspecialchars($prayer['requester_name']); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary privacy-badge">Anonymous</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div>
                                    <?php if ($prayer['my_response']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> <?php echo ucfirst($prayer['my_response']); ?>
                                        </span>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-primary" onclick="prayForRequest(<?php echo $prayer['id']; ?>, '<?php echo htmlspecialchars($prayer['title']); ?>')">
                                            <i class="bi bi-heart"></i> Pray
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <p class="mb-3"><?php echo nl2br(htmlspecialchars($prayer['description'])); ?></p>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($prayer['created_at'])); ?>
                                </small>
                                <div class="text-muted">
                                    <i class="bi bi-heart-fill"></i> <?php echo $prayer['prayed_count']; ?> prayers
                                    | <i class="bi bi-chat"></i> <?php echo $prayer['prayer_count']; ?> responses
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Prayer Request Modal -->
    <div class="modal fade" id="addPrayerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Prayer Request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Title *</label>
                            <input type="text" class="form-control" name="title" required
                                   placeholder="Brief title for your prayer request">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description *</label>
                            <textarea class="form-control" name="description" rows="4" required
                                      placeholder="Describe your prayer request in detail..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Category</label>
                                    <select class="form-select" name="category">
                                        <option value="personal">Personal</option>
                                        <option value="family">Family</option>
                                        <option value="health">Health</option>
                                        <option value="work">Work/Career</option>
                                        <option value="ministry">Ministry</option>
                                        <option value="community">Community</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Privacy Level</label>
                                    <select class="form-select" name="privacy_level">
                                        <option value="private">Private (Only me)</option>
                                        <option value="family">Family only</option>
                                        <option value="members">Members only</option>
                                        <option value="public">Public</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_urgent" id="is_urgent">
                                <label class="form-check-label" for="is_urgent">
                                    This is an urgent prayer request
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_anonymous" id="is_anonymous">
                                <label class="form-check-label" for="is_anonymous">
                                    Share anonymously (your name won't be shown)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_prayer_request" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Prayer Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Prayer Request Modal -->
    <div class="modal fade" id="editPrayerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Prayer Request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="prayer_request_id" id="edit_prayer_id">

                        <div class="mb-3">
                            <label class="form-label">Title *</label>
                            <input type="text" class="form-control" name="title" id="edit_title" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description *</label>
                            <textarea class="form-control" name="description" id="edit_description" rows="4" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Category</label>
                                    <select class="form-select" name="category" id="edit_category">
                                        <option value="personal">Personal</option>
                                        <option value="family">Family</option>
                                        <option value="health">Health</option>
                                        <option value="work">Work/Career</option>
                                        <option value="ministry">Ministry</option>
                                        <option value="community">Community</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Privacy Level</label>
                                    <select class="form-select" name="privacy_level" id="edit_privacy_level">
                                        <option value="private">Private (Only me)</option>
                                        <option value="family">Family only</option>
                                        <option value="members">Members only</option>
                                        <option value="public">Public</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_urgent" id="edit_is_urgent">
                                <label class="form-check-label" for="edit_is_urgent">
                                    This is an urgent prayer request
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_anonymous" id="edit_is_anonymous">
                                <label class="form-check-label" for="edit_is_anonymous">
                                    Share anonymously (your name won't be shown)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_prayer_request" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Prayer Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Update Prayer Status</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="prayer_request_id" id="status_prayer_id">

                        <p>Update the status for: <strong id="status_prayer_title"></strong></p>

                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status">
                                <option value="active">Active (Still need prayer)</option>
                                <option value="answered">Answered (Prayer was answered)</option>
                                <option value="closed">Closed (No longer need prayer)</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_status" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Pray for Request Modal -->
    <div class="modal fade" id="prayModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Prayer Response</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="prayer_request_id" id="pray_prayer_id">

                        <p>Thank you for praying for: <strong id="pray_prayer_title"></strong></p>

                        <div class="mb-3">
                            <label class="form-label">Response Type</label>
                            <select class="form-select" name="response_type">
                                <option value="prayed">I prayed for this</option>
                                <option value="encouragement">Offering encouragement</option>
                                <option value="support">Offering support</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Comment (Optional)</label>
                            <textarea class="form-control" name="comment" rows="3"
                                      placeholder="Share an encouraging word or verse..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_prayer_response" class="btn btn-primary">
                            <i class="bi bi-heart-fill"></i> Submit Response
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editPrayer(prayer) {
            document.getElementById('edit_prayer_id').value = prayer.id;
            document.getElementById('edit_title').value = prayer.title;
            document.getElementById('edit_description').value = prayer.description;
            document.getElementById('edit_category').value = prayer.category;
            document.getElementById('edit_privacy_level').value = prayer.privacy_level;
            document.getElementById('edit_is_urgent').checked = prayer.is_urgent == 1;
            document.getElementById('edit_is_anonymous').checked = prayer.is_anonymous == 1;

            new bootstrap.Modal(document.getElementById('editPrayerModal')).show();
        }

        function updateStatus(prayerId, prayerTitle) {
            document.getElementById('status_prayer_id').value = prayerId;
            document.getElementById('status_prayer_title').textContent = prayerTitle;

            new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
        }

        function prayForRequest(prayerId, prayerTitle) {
            document.getElementById('pray_prayer_id').value = prayerId;
            document.getElementById('pray_prayer_title').textContent = prayerTitle;

            new bootstrap.Modal(document.getElementById('prayModal')).show();
        }

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
