<?php
/**
 * User Event Sessions View
 * Allows users to view and RSVP to individual sessions of multi-session events
 */

session_start();
require_once '../church/config.php';
require_once '../church/classes/SecurityManager.php';
require_once '../church/classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Check if user has RSVP'd to main event
try {
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$event_id, $userId]);
    $main_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $main_rsvp = null;
}

// Get event sessions with user's RSVP status
try {
    $stmt = $pdo->prepare("
        SELECT es.*, 
               sr.response as user_response,
               sr.notes as user_notes,
               sr.created_at as rsvp_date,
               COUNT(sr_all.id) as total_rsvps,
               COUNT(CASE WHEN sr_all.response = 'yes' THEN 1 END) as yes_count
        FROM event_sessions es
        LEFT JOIN session_rsvps sr ON es.id = sr.session_id AND sr.member_id = ?
        LEFT JOIN session_rsvps sr_all ON es.id = sr_all.session_id
        WHERE es.event_id = ?
        GROUP BY es.id
        ORDER BY es.session_number, es.start_datetime
    ");
    $stmt->execute([$userId, $event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error loading sessions: " . $e->getMessage();
    $sessions = [];
}

// Handle session RSVP
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['session_rsvp'])) {
    $session_id = (int)$_POST['session_id'];
    $response = $_POST['response'];
    $notes = $_POST['notes'] ?? '';
    
    try {
        // Check if user already has an RSVP for this session
        $stmt = $pdo->prepare("SELECT id FROM session_rsvps WHERE session_id = ? AND member_id = ?");
        $stmt->execute([$session_id, $userId]);
        $existing_rsvp = $stmt->fetch();
        
        if ($existing_rsvp) {
            // Update existing RSVP
            $stmt = $pdo->prepare("
                UPDATE session_rsvps 
                SET response = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE session_id = ? AND member_id = ?
            ");
            $stmt->execute([$response, $notes, $session_id, $userId]);
            $message = "Your session RSVP has been updated!";
        } else {
            // Create new RSVP
            $stmt = $pdo->prepare("
                INSERT INTO session_rsvps (session_id, member_id, response, notes)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$session_id, $userId, $response, $notes]);
            $message = "Your session RSVP has been recorded!";
        }
        
        // Refresh the page to show updated data
        header("Location: event_sessions.php?event_id=" . $event_id);
        exit();
        
    } catch (PDOException $e) {
        $error = "Error saving RSVP: " . $e->getMessage();
    }
}

// Get user data for display
$userData = $userAuth->getUserById($userId);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Sessions - <?php echo htmlspecialchars($event['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/user-style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                <li class="breadcrumb-item active">Sessions</li>
            </ol>
        </nav>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Event Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2"><?php echo htmlspecialchars($event['title']); ?></h2>
                        <p class="text-muted mb-1">
                            <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?>
                            <?php if ($event['location']): ?>
                                | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                            <?php endif; ?>
                        </p>
                        <?php if ($event['description']): ?>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4 text-end">
                        <?php if ($main_rsvp): ?>
                            <span class="badge bg-success fs-6">
                                <i class="bi bi-check-circle"></i> RSVP'd to Event
                            </span>
                        <?php else: ?>
                            <a href="event_detail.php?id=<?php echo $event_id; ?>" class="btn btn-primary">
                                <i class="bi bi-calendar-plus"></i> RSVP to Main Event
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Event RSVP Notice -->
        <?php if (!$main_rsvp): ?>
            <div class="alert alert-warning">
                <h5><i class="bi bi-exclamation-triangle"></i> RSVP Required</h5>
                <p class="mb-0">You need to RSVP to the main event before you can register for individual sessions. 
                <a href="event_detail.php?id=<?php echo $event_id; ?>" class="alert-link">Click here to RSVP</a>.</p>
            </div>
        <?php endif; ?>

        <!-- Sessions -->
        <?php if (empty($sessions)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                    <h4 class="text-muted mt-3">No Sessions Available</h4>
                    <p class="text-muted">This event doesn't have individual sessions configured yet.</p>
                    <a href="events.php" class="btn btn-primary">
                        <i class="bi bi-arrow-left"></i> Back to Events
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($sessions as $session): ?>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-primary me-2">Session <?php echo $session['session_number']; ?></span>
                                    <span class="badge bg-secondary"><?php echo ucfirst($session['session_type']); ?></span>
                                    <?php if ($session['is_mandatory']): ?>
                                        <span class="badge bg-warning">Required</span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if ($session['user_response']): ?>
                                        <?php
                                        $badge_class = [
                                            'yes' => 'bg-success',
                                            'no' => 'bg-danger',
                                            'maybe' => 'bg-warning'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $badge_class[$session['user_response']]; ?>">
                                            <?php echo ucfirst($session['user_response']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($session['session_title']); ?></h5>
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> 
                                        <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?><br>
                                        <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - 
                                        <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                    </small>
                                    <?php if ($session['location']): ?>
                                        <br><small class="text-muted">
                                            <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>

                                <?php if ($session['session_description']): ?>
                                    <p class="card-text"><?php echo nl2br(htmlspecialchars($session['session_description'])); ?></p>
                                <?php endif; ?>

                                <?php if ($session['instructor_name']): ?>
                                    <p class="mb-2">
                                        <strong>Instructor:</strong> <?php echo htmlspecialchars($session['instructor_name']); ?>
                                    </p>
                                <?php endif; ?>

                                <?php if ($session['prerequisites']): ?>
                                    <div class="alert alert-info py-2">
                                        <small><strong>Prerequisites:</strong> <?php echo htmlspecialchars($session['prerequisites']); ?></small>
                                    </div>
                                <?php endif; ?>

                                <?php if ($session['materials_needed']): ?>
                                    <div class="alert alert-warning py-2">
                                        <small><strong>Materials Needed:</strong> <?php echo htmlspecialchars($session['materials_needed']); ?></small>
                                    </div>
                                <?php endif; ?>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="bi bi-people"></i> <?php echo $session['yes_count']; ?> attending
                                        <?php if ($session['max_attendees']): ?>
                                            (<?php echo $session['max_attendees']; ?> max)
                                        <?php endif; ?>
                                    </small>
                                    <?php if ($session['session_fee'] > 0): ?>
                                        <span class="badge bg-info">$<?php echo number_format($session['session_fee'], 2); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-footer">
                                <?php if ($main_rsvp && $main_rsvp['status'] === 'attending'): ?>
                                    <button type="button" class="btn btn-primary btn-sm" 
                                            onclick="showRsvpModal(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['session_title']); ?>', '<?php echo $session['user_response'] ?? ''; ?>', '<?php echo htmlspecialchars($session['user_notes'] ?? ''); ?>')">
                                        <?php echo $session['user_response'] ? 'Update RSVP' : 'RSVP to Session'; ?>
                                    </button>
                                    <?php if ($session['user_response']): ?>
                                        <small class="text-muted ms-2">
                                            RSVP'd <?php echo date('M j', strtotime($session['rsvp_date'])); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <button type="button" class="btn btn-secondary btn-sm" disabled>
                                        RSVP to Main Event First
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Session RSVP Modal -->
    <div class="modal fade" id="sessionRsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">RSVP to Session</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="session_id" id="modal_session_id">
                        
                        <h6 id="modal_session_title" class="mb-3"></h6>
                        
                        <div class="mb-3">
                            <label class="form-label">Your Response *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="response" value="yes" id="response_yes">
                                <label class="form-check-label" for="response_yes">
                                    <i class="bi bi-check-circle text-success"></i> Yes, I'll attend
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="response" value="maybe" id="response_maybe">
                                <label class="form-check-label" for="response_maybe">
                                    <i class="bi bi-question-circle text-warning"></i> Maybe
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="response" value="no" id="response_no">
                                <label class="form-check-label" for="response_no">
                                    <i class="bi bi-x-circle text-danger"></i> No, I can't attend
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" name="notes" id="modal_notes" rows="3" 
                                      placeholder="Any additional information or questions..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="session_rsvp" class="btn btn-primary">Save RSVP</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showRsvpModal(sessionId, sessionTitle, currentResponse, currentNotes) {
            document.getElementById('modal_session_id').value = sessionId;
            document.getElementById('modal_session_title').textContent = sessionTitle;
            document.getElementById('modal_notes').value = currentNotes;
            
            // Set current response if exists
            if (currentResponse) {
                document.getElementById('response_' + currentResponse).checked = true;
            } else {
                // Default to 'yes' for new RSVPs
                document.getElementById('response_yes').checked = true;
            }
            
            new bootstrap.Modal(document.getElementById('sessionRsvpModal')).show();
        }
    </script>
</body>
</html>
